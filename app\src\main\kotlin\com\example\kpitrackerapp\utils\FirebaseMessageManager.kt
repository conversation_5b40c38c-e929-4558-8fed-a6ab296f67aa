package com.example.kpitrackerapp.utils

import android.content.Context
import android.util.Log
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await

object FirebaseMessageManager {
    private const val TAG = "FirebaseMessageManager"
    private const val MESSAGES_NODE = "messages"
    private const val USERS_NODE = "users"

    private val database = FirebaseDatabase.getInstance()
    private val messaging = FirebaseMessaging.getInstance()

    /**
     * Initialize Firebase messaging for the current user
     */
    suspend fun initializeMessaging(context: Context, userId: String): Boolean {
        return try {
            // Check if Firebase is properly configured first
            if (!isFirebaseProperlyConfigured()) {
                Log.w(TAG, "Firebase not properly configured, skipping initialization")
                return false
            }

            // Get FCM token
            val token = messaging.token.await()

            // Save token locally
            saveTokenLocally(context, token)

            // Register user with token in Firebase
            registerUserToken(userId, token)

            Log.d(TAG, "Firebase messaging initialized for user: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Firebase messaging", e)
            false
        }
    }

    /**
     * Send message to specific users
     */
    suspend fun sendMessageToUsers(
        senderUserId: String,
        recipientUserIds: List<String>,
        messageType: String,
        title: String,
        content: String
    ): Boolean {
        return try {
            val messageId = System.currentTimeMillis().toString()
            val message = mapOf(
                "id" to messageId,
                "senderId" to senderUserId,
                "senderName" to "Admin", // You can get actual sender name
                "type" to messageType,
                "title" to title,
                "content" to content,
                "timestamp" to System.currentTimeMillis(),
                "recipients" to recipientUserIds
            )

            // Save message to Firebase
            database.reference
                .child(MESSAGES_NODE)
                .child(messageId)
                .setValue(message)
                .await()

            // Send to each recipient
            recipientUserIds.forEach { recipientId ->
                sendToUserInbox(recipientId, messageId, message)
            }

            Log.d(TAG, "Message sent successfully to ${recipientUserIds.size} users")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send message", e)
            false
        }
    }

    /**
     * Send message to all users
     */
    suspend fun sendMessageToAllUsers(
        senderUserId: String,
        messageType: String,
        title: String,
        content: String
    ): Boolean {
        return try {
            val messageId = System.currentTimeMillis().toString()
            val message = mapOf(
                "id" to messageId,
                "senderId" to senderUserId,
                "senderName" to "Admin",
                "type" to messageType,
                "title" to title,
                "content" to content,
                "timestamp" to System.currentTimeMillis(),
                "broadcast" to true
            )

            // Save message to Firebase
            database.reference
                .child(MESSAGES_NODE)
                .child(messageId)
                .setValue(message)
                .await()

            // Send to broadcast inbox
            database.reference
                .child("broadcast_messages")
                .child(messageId)
                .setValue(message)
                .await()

            Log.d(TAG, "Broadcast message sent successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send broadcast message", e)
            false
        }
    }

    /**
     * Get messages for a specific user
     */
    suspend fun getMessagesForUser(userId: String): List<Map<String, Any>> {
        return try {
            val messages = mutableListOf<Map<String, Any>>()

            // Get user-specific messages
            val userMessages = database.reference
                .child("user_messages")
                .child(userId)
                .get()
                .await()

            userMessages.children.forEach { snapshot ->
                val message = snapshot.value as? Map<String, Any>
                if (message != null) {
                    messages.add(message)
                }
            }

            // Get broadcast messages
            val broadcastMessages = database.reference
                .child("broadcast_messages")
                .get()
                .await()

            broadcastMessages.children.forEach { snapshot ->
                val message = snapshot.value as? Map<String, Any>
                if (message != null) {
                    messages.add(message)
                }
            }

            // Sort by timestamp
            messages.sortedByDescending {
                (it["timestamp"] as? Long) ?: 0L
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get messages for user: $userId", e)
            emptyList()
        }
    }

    /**
     * Mark message as read
     */
    suspend fun markMessageAsRead(userId: String, messageId: String): Boolean {
        return try {
            database.reference
                .child("user_messages")
                .child(userId)
                .child(messageId)
                .child("read")
                .setValue(true)
                .await()
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to mark message as read", e)
            false
        }
    }

    /**
     * Register user FCM token
     */
    private suspend fun registerUserToken(userId: String, token: String) {
        try {
            val userTokenData = mapOf(
                "userId" to userId,
                "token" to token,
                "lastUpdated" to System.currentTimeMillis()
            )

            database.reference
                .child(USERS_NODE)
                .child(userId)
                .child("fcm_token")
                .setValue(userTokenData)
                .await()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to register user token", e)
        }
    }

    /**
     * Send message to user's inbox
     */
    private suspend fun sendToUserInbox(userId: String, messageId: String, message: Map<String, Any>) {
        try {
            val inboxMessage = message.toMutableMap()
            inboxMessage["read"] = false
            inboxMessage["receivedAt"] = System.currentTimeMillis()

            database.reference
                .child("user_messages")
                .child(userId)
                .child(messageId)
                .setValue(inboxMessage)
                .await()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send message to user inbox: $userId", e)
        }
    }

    /**
     * Save FCM token locally
     */
    private fun saveTokenLocally(context: Context, token: String) {
        val prefs = context.getSharedPreferences("firebase_prefs", Context.MODE_PRIVATE)
        prefs.edit().putString("fcm_token", token).apply()
    }

    /**
     * Get saved FCM token
     */
    fun getSavedToken(context: Context): String? {
        val prefs = context.getSharedPreferences("firebase_prefs", Context.MODE_PRIVATE)
        return prefs.getString("fcm_token", null)
    }

    /**
     * Check if Firebase is available (for offline fallback)
     */
    fun isFirebaseAvailable(): Boolean {
        return try {
            FirebaseDatabase.getInstance()
            isFirebaseProperlyConfigured()
        } catch (e: Exception) {
            Log.w(TAG, "Firebase not available, using offline mode")
            false
        }
    }

    /**
     * Check if Firebase is properly configured with valid API key
     */
    private fun isFirebaseProperlyConfigured(): Boolean {
        return try {
            // Check if we have a valid Firebase app configuration
            val app = com.google.firebase.FirebaseApp.getInstance()
            val apiKey = app.options.apiKey

            // Check if API key is not the demo/placeholder key
            val isDemoKey = apiKey.contains("Demo") ||
                           apiKey.contains("demo") ||
                           apiKey == "AIzaSyDemoKeyForKPITrackerApp123456789"

            if (isDemoKey) {
                Log.w(TAG, "Demo Firebase API key detected, Firebase features disabled")
                return false
            }

            true
        } catch (e: Exception) {
            Log.w(TAG, "Firebase configuration check failed", e)
            false
        }
    }
}
