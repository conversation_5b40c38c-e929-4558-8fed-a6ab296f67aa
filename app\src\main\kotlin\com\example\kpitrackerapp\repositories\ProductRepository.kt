package com.example.kpitrackerapp.repositories

import com.example.kpitrackerapp.models.Product
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.persistence.ProductDao
import kotlinx.coroutines.flow.Flow
import java.util.Calendar
import java.util.Date

class ProductRepository(
    private val productDao: ProductDao
) {

    fun getAllProducts(): Flow<List<Product>> = productDao.getAllProducts()

    fun getProductById(productId: String): Flow<Product?> = productDao.getProductById(productId)

    fun getProductsByCategory(category: ProductCategory): Flow<List<Product>> =
        productDao.getProductsByCategory(category)

    fun getExpiredProducts(): Flow<List<Product>> {
        val currentDate = Date()
        return productDao.getExpiredProducts(currentDate)
    }

    fun getProductsExpiringInDays(days: Int): Flow<List<Product>> {
        val currentDate = Date()
        val calendar = Calendar.getInstance()
        calendar.time = currentDate
        calendar.add(Calendar.DAY_OF_YEAR, days)
        val endDate = calendar.time

        return productDao.getProductsExpiringInRange(currentDate, endDate)
    }

    suspend fun insertProduct(product: Product): Long {
        val updatedProduct = product.copy(
            daysUntilExpiry = calculateDaysUntilExpiry(product.expiryDate),
            isExpired = isProductExpired(product.expiryDate)
        )
        return productDao.insertProduct(updatedProduct)
    }

    suspend fun updateProduct(product: Product) {
        val updatedProduct = product.copy(
            updatedAt = Date(),
            daysUntilExpiry = calculateDaysUntilExpiry(product.expiryDate),
            isExpired = isProductExpired(product.expiryDate)
        )
        productDao.updateProduct(updatedProduct)
    }

    suspend fun deleteProduct(product: Product) = productDao.deleteProduct(product)

    suspend fun getTotalProductsCount(): Int = productDao.getTotalProductsCount()

    suspend fun getExpiredProductsCount(): Int {
        val currentDate = Date()
        return productDao.getExpiredProductsCount(currentDate)
    }

    suspend fun getProductsExpiringInDaysCount(days: Int): Int {
        val currentDate = Date()
        val calendar = Calendar.getInstance()
        calendar.time = currentDate
        calendar.add(Calendar.DAY_OF_YEAR, days)
        val endDate = calendar.time

        return productDao.getProductsExpiringInRangeCount(currentDate, endDate)
    }

    suspend fun getProductsCountByCategory(category: ProductCategory): Int =
        productDao.getProductsCountByCategory(category)

    suspend fun deleteExpiredProductsOlderThan(days: Int): Int {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -days)
        val cutoffDate = calendar.time

        return productDao.deleteExpiredProductsOlderThan(cutoffDate)
    }

    private fun calculateDaysUntilExpiry(expiryDate: Date): Int {
        val currentDate = Date()
        val diffInMillis = expiryDate.time - currentDate.time
        return (diffInMillis / (1000 * 60 * 60 * 24)).toInt()
    }

    private fun isProductExpired(expiryDate: Date): Boolean {
        return expiryDate.before(Date())
    }
}
