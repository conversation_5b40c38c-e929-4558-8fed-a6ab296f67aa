package com.example.kpitrackerapp.viewmodels

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.kpitrackerapp.persistence.ProductDao
import com.example.kpitrackerapp.repositories.ProductRepository

/**
 * Factory for creating ProductViewModel instances with required dependencies.
 */
class ProductViewModelFactory(
    private val application: Application,
    private val productDao: ProductDao
) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ProductViewModel::class.java)) {
            val productRepository = ProductRepository(productDao)
            return ProductViewModel(productRepository) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
