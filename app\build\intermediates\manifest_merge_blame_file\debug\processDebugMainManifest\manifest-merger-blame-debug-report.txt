1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.kpitrackerapp"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Required for notifications on Android 13+ -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:5-76
12-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:6:22-74
13    <!-- Required for running foreground services (used by WorkManager when setForeground is called) -->
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:8:22-74
15    <!-- Required for Camera -->
16    <uses-permission android:name="android.permission.CAMERA" />
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:5-64
16-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:10:22-62
17    <!-- Required for Calendar Integration -->
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:5-72
18-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:5-73
19-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:13:22-70
20    <!-- Required for Speech Recognition -->
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:5-71
21-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:15:22-68
22    <uses-permission android:name="android.permission.INTERNET" />
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:5-67
22-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:16:22-64
23    <!-- Required for Location Services -->
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:5-79
24-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:18:22-76
25    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:5-81
25-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:19:22-78
26    <!-- Required for Setting Alarms -->
27    <uses-permission android:name="android.permission.SET_ALARM" />
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:5-68
27-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:21:22-65
28    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:5-78
28-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:22:22-75
29
30    <!-- Declare camera feature, but don't require it if app can function without -->
31    <uses-feature
31-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:5-84
32        android:name="android.hardware.camera"
32-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:19-57
33        android:required="false" />
33-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:25:58-82
34
35    <!-- Queries for specific apps we want to detect (Android 11+ requirement) -->
36    <queries>
36-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:28:5-40:15
37
38        <!-- WhatsApp Regular -->
39        <package android:name="com.whatsapp" />
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:9-48
39-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:30:18-45
40        <!-- WhatsApp Business -->
41        <package android:name="com.whatsapp.w4b" />
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:9-52
41-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:32:18-49
42        <!-- Gmail -->
43        <package android:name="com.google.android.gm" />
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:9-57
43-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:34:18-54
44        <!-- Email intent -->
45        <intent>
45-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:36:9-39:18
46            <action android:name="android.intent.action.SENDTO" />
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:13-67
46-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:37:21-64
47
48            <data android:scheme="mailto" />
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:13-45
48-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:38:19-42
49        </intent>
50    </queries>
51
52    <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
53    <!-- <uses-sdk android:minSdkVersion="14"/> -->
54    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:5-79
54-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:22:22-76
55    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
55-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:24:22-65
56    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
56-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
57    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
57-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
58-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
59-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
60    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
60-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
61    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
61-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
62
63    <permission
63-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
64        android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.example.kpitrackerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
68
69    <application
69-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:42:5-282:19
70        android:name="com.example.kpitrackerapp.KpiTrackerApplication"
70-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:43:9-46
71        android:allowBackup="true"
71-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:44:9-35
72        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
72-->[androidx.core:core:1.12.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\d69c07a7d8d82aa3a4b3977e5b803b90\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
73        android:dataExtractionRules="@xml/data_extraction_rules"
73-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:45:9-65
74        android:debuggable="true"
75        android:extractNativeLibs="false"
76        android:fullBackupContent="@xml/backup_rules"
76-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:46:9-54
77        android:icon="@mipmap/ic_launcher"
77-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:48:9-43
78        android:label="@string/app_name"
78-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:47:9-41
79        android:networkSecurityConfig="@xml/network_security_config"
79-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:52:9-69
80        android:roundIcon="@mipmap/ic_launcher"
80-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:49:9-48
81        android:supportsRtl="true"
81-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:50:9-35
82        android:testOnly="true"
83        android:theme="@style/Theme.KPITrackerApp" >
83-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:51:9-51
84
85        <!-- Optional: Request OCR module download on install/update -->
86        <meta-data
86-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:56:9-58:35
87            android:name="com.google.mlkit.vision.DEPENDENCIES"
87-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:57:13-64
88            android:value="ocr" />
88-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:58:13-32
89
90        <activity
90-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:60:9-68:20
91            android:name="com.example.kpitrackerapp.MainActivity"
91-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:61:13-41
92            android:exported="true"
92-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:62:13-36
93            android:theme="@style/Theme.KPITrackerApp.NoActionBar" > <!-- Apply NoActionBar theme here -->
93-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:63:13-67
94            <intent-filter>
94-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:64:13-67:29
95                <action android:name="android.intent.action.MAIN" />
95-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:17-69
95-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:65:25-66
96
97                <category android:name="android.intent.category.LAUNCHER" />
97-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:17-77
97-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:66:27-74
98            </intent-filter>
99        </activity>
100        <activity
100-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:69:9-73:70
101            android:name="com.example.kpitrackerapp.ui.AddEditKpiActivity"
101-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:70:13-50
102            android:label="@string/add_kpi_title"
102-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:71:13-50
103            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
103-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:72:13-55
104            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- This is now for Tasks -->
104-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:73:13-67
105        <activity
105-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:74:9-78:70
106            android:name="com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity"
106-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:75:13-58
107            android:label="@string/add_kpi_title"
107-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:76:13-50
108            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
108-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:77:13-55
109            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
109-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:78:13-67
110        <activity
110-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:79:9-82:58
111            android:name="com.example.kpitrackerapp.ui.KpiDetailActivity"
111-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:80:13-49
112            android:label="@string/kpi_detail_title"
112-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:81:13-53
113            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
113-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:82:13-55
114        <activity
114-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:83:9-87:70
115            android:name="com.example.kpitrackerapp.ui.ModernReportActivity"
115-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:84:13-52
116            android:label="Interactive Performance Report"
116-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:85:13-59
117            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
117-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:86:13-55
118            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
118-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:87:13-67
119        <activity
119-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:88:9-91:58
120            android:name="com.example.kpitrackerapp.ui.ExpireManagementActivity"
120-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:89:13-56
121            android:label="@string/action_expiry_management"
121-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:90:13-61
122            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
122-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:91:13-55
123        <activity
123-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:92:9-95:66
124            android:name="com.example.kpitrackerapp.ui.SearchEditProgressActivity"
124-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:93:13-58
125            android:label="@string/search_edit_progress_title"
125-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:94:13-63
126            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
126-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:95:13-63
127        <activity
127-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:96:9-99:58
128            android:name="com.example.kpitrackerapp.ui.OcrActivity"
128-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:97:13-43
129            android:label="@string/ocr_activity_title"
129-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:98:13-55
130            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
130-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:99:13-55
131        <activity
131-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:100:9-103:60
132            android:name="com.example.kpitrackerapp.ui.OcrReviewActivity"
132-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:101:13-49
133            android:label="@string/review_ocr_results_title"
133-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:102:13-61
134            android:parentActivityName="com.example.kpitrackerapp.ui.OcrActivity" /> <!-- Parent is OcrActivity -->
134-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:103:13-57
135        <!-- Removed SmartListAnalysisActivity declaration -->
136        <activity
136-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:105:9-108:66
137            android:name="com.example.kpitrackerapp.ui.ExcelImportActivity"
137-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:106:13-51
138            android:label="Import from Excel"
138-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:107:13-46
139            android:parentActivityName="com.example.kpitrackerapp.ui.KpiDetailActivity" />
139-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:108:13-63
140        <activity
140-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:109:9-112:68
141            android:name="com.example.kpitrackerapp.ui.ExcelReviewActivity"
141-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:110:13-51
142            android:label="Review Excel Import"
142-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:111:13-48
143            android:parentActivityName="com.example.kpitrackerapp.ui.ExcelImportActivity" />
143-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:112:13-65
144        <activity
144-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:114:9-118:70
145            android:name="com.example.kpitrackerapp.ui.UserKpiListActivity"
145-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:115:13-51
146            android:label="@string/user_kpi_list_title"
146-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:116:13-56
147            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
147-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:117:13-55
148            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- Apply NoActionBar theme -->
148-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:118:13-67
149        <activity
149-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:119:9-123:70
150            android:name="com.example.kpitrackerapp.ui.TaskManagementActivity"
150-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:120:13-54
151            android:label="Task Management"
151-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:121:13-44
152            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
152-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:122:13-55
153            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
153-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:123:13-67
154        <activity
154-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:124:9-128:70
155            android:name="com.example.kpitrackerapp.ui.TaskReportActivity"
155-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:125:13-50
156            android:label="Task Reports"
156-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:126:13-41
157            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
157-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:127:13-68
158            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
158-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:128:13-67
159
160        <!-- Login and User Management Activities -->
161        <activity
161-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:131:9-135:40
162            android:name="com.example.kpitrackerapp.ui.LoginActivity"
162-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:132:13-45
163            android:exported="false"
163-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:135:13-37
164            android:label="Login"
164-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:133:13-34
165            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
165-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:134:13-67
166        <activity
166-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:136:9-141:40
167            android:name="com.example.kpitrackerapp.ui.CreateUserActivity"
167-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:137:13-50
168            android:exported="false"
168-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:141:13-37
169            android:label="Create User"
169-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:138:13-40
170            android:parentActivityName="com.example.kpitrackerapp.ui.LoginActivity"
170-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:139:13-59
171            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
171-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:140:13-67
172
173        <!-- Admin Dashboard Activity -->
174        <activity
174-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:144:9-149:40
175            android:name="com.example.kpitrackerapp.AdminDashboardActivity"
175-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:145:13-51
176            android:exported="false"
176-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:149:13-37
177            android:label="Admin Dashboard"
177-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:146:13-44
178            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
178-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:147:13-55
179            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
179-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:148:13-67
180
181        <!-- Product Management Activities -->
182        <activity
182-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:152:9-157:40
183            android:name="com.example.kpitrackerapp.ui.AddProductActivity"
183-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:153:13-50
184            android:exported="false"
184-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:157:13-37
185            android:label="Add Product"
185-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:154:13-40
186            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
186-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:155:13-70
187            android:theme="@style/Theme.KPITrackerApp" />
187-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:156:13-55
188        <activity
188-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:158:9-163:40
189            android:name="com.example.kpitrackerapp.ui.ProductListActivity"
189-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:159:13-51
190            android:exported="false"
190-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:163:13-37
191            android:label="Product List"
191-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:160:13-41
192            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
192-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:161:13-70
193            android:theme="@style/Theme.KPITrackerApp" />
193-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:162:13-55
194        <activity
194-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:164:9-169:40
195            android:name="com.example.kpitrackerapp.ui.ExpiryTrackingActivity"
195-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:165:13-54
196            android:exported="false"
196-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:169:13-37
197            android:label="Expiry Tracking"
197-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:166:13-44
198            android:parentActivityName="com.example.kpitrackerapp.ui.ExpireManagementActivity"
198-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:167:13-70
199            android:theme="@style/Theme.KPITrackerApp" />
199-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:168:13-55
200
201        <!-- Chat Activities -->
202        <activity
202-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:172:9-177:40
203            android:name="com.example.kpitrackerapp.ui.ChatListActivity"
203-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:173:13-48
204            android:exported="false"
204-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:177:13-37
205            android:label="Messages"
205-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:174:13-37
206            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
206-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:175:13-55
207            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
207-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:176:13-67
208        <activity
208-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:179:9-184:40
209            android:name="com.example.kpitrackerapp.ui.ChatActivity"
209-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:180:13-44
210            android:exported="false"
210-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:184:13-37
211            android:label="Chat"
211-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:181:13-33
212            android:parentActivityName="com.example.kpitrackerapp.ui.ChatListActivity"
212-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:182:13-62
213            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
213-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:183:13-67
214
215        <!-- Date Converter Activity -->
216        <activity
216-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:187:9-192:40
217            android:name="com.example.kpitrackerapp.ui.DateConverterActivity"
217-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:188:13-53
218            android:exported="false"
218-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:192:13-37
219            android:label="محول التاريخ"
219-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:189:13-41
220            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
220-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:190:13-55
221            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
221-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:191:13-67
222
223        <!-- Task Reminder Settings Activity -->
224        <activity
224-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:195:9-200:40
225            android:name="com.example.kpitrackerapp.ui.TaskReminderSettingsActivity"
225-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:196:13-60
226            android:exported="false"
226-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:200:13-37
227            android:label="Task Reminder Settings"
227-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:197:13-51
228            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
228-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:198:13-68
229            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
229-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:199:13-67
230
231        <!-- Auto Send Settings Activity -->
232        <activity
232-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:203:9-208:40
233            android:name="com.example.kpitrackerapp.ui.AutoSendSettingsActivity"
233-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:204:13-56
234            android:exported="false"
234-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:208:13-37
235            android:label="Auto Send Settings"
235-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:205:13-47
236            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
236-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:206:13-55
237            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
237-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:207:13-67
238
239        <!-- Advanced Task Activity -->
240        <activity
240-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:211:9-216:40
241            android:name="com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity"
241-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:212:13-59
242            android:exported="false"
242-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:216:13-37
243            android:label="إضافة مهمة متقدمة"
243-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:213:13-46
244            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
244-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:214:13-55
245            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
245-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:215:13-67
246
247        <!-- Modern Add Task Activity -->
248        <activity
248-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:219:9-224:40
249            android:name="com.example.kpitrackerapp.ui.ModernAddTaskActivity"
249-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:220:13-53
250            android:exported="false"
250-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:224:13-37
251            android:label="Add New Task"
251-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:221:13-41
252            android:parentActivityName="com.example.kpitrackerapp.MainActivity"
252-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:222:13-55
253            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
253-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:223:13-67
254
255        <!-- Pomodoro Timer Activity -->
256        <activity
256-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:227:9-233:52
257            android:name="com.example.kpitrackerapp.ui.PomodoroTimerActivity"
257-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:228:13-53
258            android:exported="false"
258-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:232:13-37
259            android:label="Pomodoro Timer"
259-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:229:13-43
260            android:parentActivityName="com.example.kpitrackerapp.ui.TaskManagementActivity"
260-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:230:13-68
261            android:screenOrientation="portrait"
261-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:233:13-49
262            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
262-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:231:13-67
263
264        <!-- Add other activities, services, etc. here -->
265        <activity
265-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:236:9-239:58
266            android:name="com.example.kpitrackerapp.ui.NotificationsActivity"
266-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:237:13-53
267            android:exported="false"
267-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:238:13-37
268            android:parentActivityName="com.example.kpitrackerapp.MainActivity" />
268-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:239:13-55
269
270        <!-- Firebase Messaging Service -->
271        <service
271-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:242:9-248:19
272            android:name="com.example.kpitrackerapp.services.KPIFirebaseMessagingService"
272-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:243:13-65
273            android:exported="false" >
273-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:244:13-37
274            <intent-filter>
274-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:245:13-247:29
275                <action android:name="com.google.firebase.MESSAGING_EVENT" />
275-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:246:17-78
275-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:246:25-75
276            </intent-filter>
277        </service>
278
279        <!-- Firebase Messaging default notification icon -->
280        <meta-data
280-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:251:9-253:60
281            android:name="com.google.firebase.messaging.default_notification_icon"
281-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:252:13-83
282            android:resource="@drawable/ic_notification" />
282-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:253:13-57
283
284        <!-- Firebase Messaging default notification color -->
285        <meta-data
285-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:256:9-258:52
286            android:name="com.google.firebase.messaging.default_notification_color"
286-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:257:13-84
287            android:resource="@color/purple_500" />
287-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:258:13-49
288
289        <!-- FileProvider for sharing camera image URI -->
290        <provider
291            android:name="androidx.core.content.FileProvider"
291-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:262:13-62
292            android:authorities="com.example.kpitrackerapp.provider"
292-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:263:13-60
293            android:exported="false"
293-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:264:13-37
294            android:grantUriPermissions="true" >
294-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:265:13-47
295            <meta-data
295-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:266:13-268:54
296                android:name="android.support.FILE_PROVIDER_PATHS"
296-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:267:17-67
297                android:resource="@xml/file_paths" />
297-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:268:17-51
298        </provider>
299
300        <!-- Disable WorkManager automatic initialization since we use Configuration.Provider -->
301        <provider
302            android:name="androidx.startup.InitializationProvider"
302-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:273:13-67
303            android:authorities="com.example.kpitrackerapp.androidx-startup"
303-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:274:13-68
304            android:exported="false" >
304-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:275:13-37
305            <meta-data
305-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
306                android:name="androidx.emoji2.text.EmojiCompatInitializer"
306-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
307                android:value="androidx.startup" />
307-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\a5046b8718eb7718c37e191f5a52f4c5\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
308            <meta-data
308-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
309                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
309-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
310                android:value="androidx.startup" />
310-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\8979a47168c13891bf6142b3909bb09c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
311            <meta-data
311-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
312                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
312-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
313                android:value="androidx.startup" />
313-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
314        </provider>
315
316        <service
316-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
317            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
317-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
318            android:directBootAware="true"
318-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:17:13-43
319            android:exported="false" >
319-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
320            <meta-data
320-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
321                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
321-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
322                android:value="com.google.firebase.components.ComponentRegistrar" />
322-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\3b0d65b1b551734c1cd2940b53140a0b\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
323            <meta-data
323-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
324                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
324-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
325                android:value="com.google.firebase.components.ComponentRegistrar" />
325-->[com.google.mlkit:vision-common:17.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\1063076d438e1847fa9d9ed50fcacbf0\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
326            <meta-data
326-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:20:13-22:85
327                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
327-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:21:17-120
328                android:value="com.google.firebase.components.ComponentRegistrar" />
328-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:22:17-82
329        </service>
330
331        <provider
331-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:9:9-13:38
332            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
332-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:10:13-78
333            android:authorities="com.example.kpitrackerapp.mlkitinitprovider"
333-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:11:13-69
334            android:exported="false"
334-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:12:13-37
335            android:initOrder="99" />
335-->[com.google.mlkit:common:18.8.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\bd5b3404a1ec234c54c19f6d413719ec\transformed\jetified-common-18.8.0\AndroidManifest.xml:13:13-35
336
337        <service
337-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:9:9-15:19
338            android:name="com.google.firebase.components.ComponentDiscoveryService"
338-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:10:13-84
339            android:directBootAware="true"
339-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
340            android:exported="false" >
340-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:11:13-37
341            <meta-data
341-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:12:13-14:85
342                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
342-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:13:17-127
343                android:value="com.google.firebase.components.ComponentRegistrar" />
343-->[com.google.firebase:firebase-database-ktx:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\5828c11a8c980f91e1b7ee1082d48790\transformed\jetified-firebase-database-ktx-20.3.0\AndroidManifest.xml:14:17-82
344            <meta-data
344-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:29:13-31:85
345                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
345-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:30:17-120
346                android:value="com.google.firebase.components.ComponentRegistrar" />
346-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:31:17-82
347            <meta-data
347-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:32:13-34:85
348                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
348-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:33:17-109
349                android:value="com.google.firebase.components.ComponentRegistrar" />
349-->[com.google.firebase:firebase-database:20.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ccae4dd816f6bc175f2d8b3f271ffae9\transformed\jetified-firebase-database-20.3.0\AndroidManifest.xml:34:17-82
350            <meta-data
350-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
351                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
351-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
352                android:value="com.google.firebase.components.ComponentRegistrar" />
352-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\80cfd5dd6a9dcc5f71fdfc2bfdc91067\transformed\jetified-firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
353            <meta-data
353-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
354                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
354-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
355                android:value="com.google.firebase.components.ComponentRegistrar" />
355-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
356            <meta-data
356-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
357                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
357-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
358                android:value="com.google.firebase.components.ComponentRegistrar" />
358-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
359            <meta-data
359-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
360                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
360-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
361                android:value="com.google.firebase.components.ComponentRegistrar" />
361-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\e1e5edf221d9e2eedd44e81a2a865970\transformed\jetified-firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
362            <meta-data
362-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
363                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
363-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
364                android:value="com.google.firebase.components.ComponentRegistrar" />
364-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
365            <meta-data
365-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
366                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
366-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
367                android:value="com.google.firebase.components.ComponentRegistrar" />
367-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
368            <meta-data
368-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
369                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
369-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
370                android:value="com.google.firebase.components.ComponentRegistrar" />
370-->[com.google.firebase:firebase-installations:17.2.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2c98800c229cdb288e18ef4e9671af13\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
371            <meta-data
371-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
372                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
372-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
373                android:value="com.google.firebase.components.ComponentRegistrar" />
373-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\0d006008fe6bff343360bc682c7a0697\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
374            <meta-data
374-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
375                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
375-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
376                android:value="com.google.firebase.components.ComponentRegistrar" />
376-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
377            <meta-data
377-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
378                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
378-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
379                android:value="com.google.firebase.components.ComponentRegistrar" />
379-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\b7c9fb2db29bcc8c4004f9c995f69ff4\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
380        </service>
381
382        <receiver
382-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
383            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
383-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
384            android:exported="true"
384-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
385            android:permission="com.google.android.c2dm.permission.SEND" >
385-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
386            <intent-filter>
386-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
387                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
387-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
387-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
388            </intent-filter>
389
390            <meta-data
390-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
391                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
391-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
392                android:value="true" />
392-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
393        </receiver>
394        <!--
395             FirebaseMessagingService performs security checks at runtime,
396             but set to not exported to explicitly avoid allowing another app to call it.
397        -->
398        <service
398-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
399            android:name="com.google.firebase.messaging.FirebaseMessagingService"
399-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
400            android:directBootAware="true"
400-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
401            android:exported="false" >
401-->[com.google.firebase:firebase-messaging:23.4.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\7ade2ffb4f9a940dbc99d2e64ef30d9c\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
402            <intent-filter android:priority="-500" >
402-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:245:13-247:29
403                <action android:name="com.google.firebase.MESSAGING_EVENT" />
403-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:246:17-78
403-->D:\copy from kpi tracker app\kpi-tracker-app void test 2\app\src\main\AndroidManifest.xml:246:25-75
404            </intent-filter>
405        </service>
406
407        <activity
407-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
408            android:name="com.google.android.gms.common.api.GoogleApiActivity"
408-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
409            android:exported="false"
409-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
410            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
410-->[com.google.android.gms:play-services-base:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\32f7978bc74fb055e711be763c16e3bd\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
411
412        <property
412-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
413            android:name="android.adservices.AD_SERVICES_CONFIG"
413-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
414            android:resource="@xml/ga_ad_services_config" />
414-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\08385018a41926e4f5f8203b74b245a4\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
415
416        <provider
416-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
417            android:name="com.google.firebase.provider.FirebaseInitProvider"
417-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
418            android:authorities="com.example.kpitrackerapp.firebaseinitprovider"
418-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
419            android:directBootAware="true"
419-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
420            android:exported="false"
420-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
421            android:initOrder="100" />
421-->[com.google.firebase:firebase-common:20.4.2] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\2ff50890794a236cfeb953e514960bd0\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
422
423        <service
423-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
424            android:name="androidx.room.MultiInstanceInvalidationService"
424-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
425            android:directBootAware="true"
425-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
426            android:exported="false" />
426-->[androidx.room:room-runtime:2.6.1] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\ab9026b9dd4f039d9e1cf433c0318cf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
427        <service
427-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
428            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
428-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
429            android:directBootAware="false"
429-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
430            android:enabled="@bool/enable_system_alarm_service_default"
430-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
431            android:exported="false" />
431-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
432        <service
432-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
433            android:name="androidx.work.impl.background.systemjob.SystemJobService"
433-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
434            android:directBootAware="false"
434-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
435            android:enabled="@bool/enable_system_job_service_default"
435-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
436            android:exported="true"
436-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
437            android:permission="android.permission.BIND_JOB_SERVICE" />
437-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
438        <service
438-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
439            android:name="androidx.work.impl.foreground.SystemForegroundService"
439-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
440            android:directBootAware="false"
440-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
441            android:enabled="@bool/enable_system_foreground_service_default"
441-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
442            android:exported="false" />
442-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
443
444        <receiver
444-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
445            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
445-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
446            android:directBootAware="false"
446-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
447            android:enabled="true"
447-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
448            android:exported="false" />
448-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
449        <receiver
449-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
450            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
450-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
451            android:directBootAware="false"
451-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
452            android:enabled="false"
452-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
453            android:exported="false" >
453-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
454            <intent-filter>
454-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
455                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
455-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
455-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
456                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
456-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
456-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
457            </intent-filter>
458        </receiver>
459        <receiver
459-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
460            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
460-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
461            android:directBootAware="false"
461-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
462            android:enabled="false"
462-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
463            android:exported="false" >
463-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
464            <intent-filter>
464-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
465                <action android:name="android.intent.action.BATTERY_OKAY" />
465-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
465-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
466                <action android:name="android.intent.action.BATTERY_LOW" />
466-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
466-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
467            </intent-filter>
468        </receiver>
469        <receiver
469-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
470            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
470-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
472            android:enabled="false"
472-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
473            android:exported="false" >
473-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
474            <intent-filter>
474-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
475                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
475-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
475-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
476                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
476-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
476-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
477            </intent-filter>
478        </receiver>
479        <receiver
479-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
480            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
480-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
481            android:directBootAware="false"
481-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
482            android:enabled="false"
482-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
483            android:exported="false" >
483-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
484            <intent-filter>
484-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
485                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
485-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
485-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
486            </intent-filter>
487        </receiver>
488        <receiver
488-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
489            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
489-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
490            android:directBootAware="false"
490-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
491            android:enabled="false"
491-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
492            android:exported="false" >
492-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
493            <intent-filter>
493-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
494                <action android:name="android.intent.action.BOOT_COMPLETED" />
494-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
494-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
495                <action android:name="android.intent.action.TIME_SET" />
495-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
495-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
496                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
496-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
496-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
497            </intent-filter>
498        </receiver>
499        <receiver
499-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
500            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
500-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
501            android:directBootAware="false"
501-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
502            android:enabled="@bool/enable_system_alarm_service_default"
502-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
503            android:exported="false" >
503-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
504            <intent-filter>
504-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
505                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
505-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
505-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
506            </intent-filter>
507        </receiver>
508        <receiver
508-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
509            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
509-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
510            android:directBootAware="false"
510-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
511            android:enabled="true"
511-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
512            android:exported="true"
512-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
513            android:permission="android.permission.DUMP" >
513-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
514            <intent-filter>
514-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
515                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
515-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
515-->[androidx.work:work-runtime:2.9.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\76b4eea8734a042dfcf25a6f59756729\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
516            </intent-filter>
517        </receiver>
518        <receiver
518-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
519            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
519-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
520            android:enabled="true"
520-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
521            android:exported="false" >
521-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
522        </receiver>
523
524        <service
524-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
525            android:name="com.google.android.gms.measurement.AppMeasurementService"
525-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
526            android:enabled="true"
526-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
527            android:exported="false" />
527-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
528        <service
528-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
529            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
529-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
530            android:enabled="true"
530-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
531            android:exported="false"
531-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
532            android:permission="android.permission.BIND_JOB_SERVICE" />
532-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\00e9c1b45d2496dd81336c8b50048011\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
533
534        <uses-library
534-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
535            android:name="android.ext.adservices"
535-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
536            android:required="false" />
536-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\64b2a28493382675ec9ae515f82793b7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
537
538        <meta-data
538-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
539            android:name="com.google.android.gms.version"
539-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
540            android:value="@integer/google_play_services_version" />
540-->[com.google.android.gms:play-services-basement:18.1.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\44fac508c06d1fa71634b2a2e87bd97f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
541
542        <receiver
542-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
543            android:name="androidx.profileinstaller.ProfileInstallReceiver"
543-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
544            android:directBootAware="false"
544-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
545            android:enabled="true"
545-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
546            android:exported="true"
546-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
547            android:permission="android.permission.DUMP" >
547-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
548            <intent-filter>
548-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
549                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
549-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
549-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
550            </intent-filter>
551            <intent-filter>
551-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
552                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
552-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
552-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
553            </intent-filter>
554            <intent-filter>
554-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
555                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
555-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
555-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
556            </intent-filter>
557            <intent-filter>
557-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
558                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
558-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
558-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\dbe66d54dab4ee72c8e559e8e87ba271\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
559            </intent-filter>
560        </receiver>
561
562        <service
562-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
563            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
563-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
564            android:exported="false" >
564-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
565            <meta-data
565-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
566                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
566-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
567                android:value="cct" />
567-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\47d0a1125fa9e6e19ba256fd0b797d4e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
568        </service>
569        <service
569-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
570            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
570-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
571            android:exported="false"
571-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
572            android:permission="android.permission.BIND_JOB_SERVICE" >
572-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
573        </service>
574
575        <receiver
575-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
576            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
576-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
577            android:exported="false" />
577-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Gradle\gradle-8.4\caches\8.11.1\transforms\fd0549f9c549c20cc0dcd3827ae8f960\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
578    </application>
579
580</manifest>
