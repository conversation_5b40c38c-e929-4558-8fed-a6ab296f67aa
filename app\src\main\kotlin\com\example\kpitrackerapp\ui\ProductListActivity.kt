package com.example.kpitrackerapp.ui

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.adapters.ProductListAdapter
import com.example.kpitrackerapp.databinding.ActivityProductListBinding
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.persistence.AppDatabase
import com.example.kpitrackerapp.viewmodels.ProductViewModel
import com.example.kpitrackerapp.viewmodels.ProductViewModelFactory
import kotlinx.coroutines.launch

class ProductListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductListBinding
    private val productViewModel: ProductViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        ProductViewModelFactory(
            application,
            database.productDao()
        )
    }
    private lateinit var adapter: ProductListAdapter

    companion object {
        const val EXTRA_FILTER_TYPE = "filter_type"
        const val EXTRA_CATEGORY = "category"
        const val FILTER_ALL = "all"
        const val FILTER_EXPIRED = "expired"
        const val FILTER_EXPIRING_SOON = "expiring_soon"
        const val FILTER_CATEGORY = "category"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupActionBar()
        setupRecyclerView()
        setupFilter()
        observeProducts()
    }

    private fun setupActionBar() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        val filterType = intent.getStringExtra(EXTRA_FILTER_TYPE) ?: FILTER_ALL
        val category = intent.getStringExtra(EXTRA_CATEGORY)
        
        title = when (filterType) {
            FILTER_ALL -> "جميع المنتجات"
            FILTER_EXPIRED -> "منتجات منتهية الصلاحية"
            FILTER_EXPIRING_SOON -> "منتجات تنتهي قريباً"
            FILTER_CATEGORY -> {
                val categoryEnum = ProductCategory.valueOf(category ?: "FOOD")
                "${categoryEnum.emoji} ${categoryEnum.displayName}"
            }
            else -> "المنتجات"
        }
    }

    private fun setupRecyclerView() {
        adapter = ProductListAdapter { product ->
            // Handle product click - could open detail view
        }
        
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
    }

    private fun setupFilter() {
        val filterType = intent.getStringExtra(EXTRA_FILTER_TYPE) ?: FILTER_ALL
        val category = intent.getStringExtra(EXTRA_CATEGORY)

        lifecycleScope.launch {
            val flow = when (filterType) {
                FILTER_ALL -> productViewModel.allProducts
                FILTER_EXPIRED -> productViewModel.expiredProducts
                FILTER_EXPIRING_SOON -> productViewModel.getProductsExpiringInDays(7)
                FILTER_CATEGORY -> {
                    val categoryEnum = ProductCategory.valueOf(category ?: "FOOD")
                    productViewModel.getProductsByCategory(categoryEnum)
                }
                else -> productViewModel.allProducts
            }

            flow.collect { products ->
                adapter.submitList(products)
                
                // Update empty state
                if (products.isEmpty()) {
                    binding.emptyStateText.text = when (filterType) {
                        FILTER_EXPIRED -> "لا توجد منتجات منتهية الصلاحية"
                        FILTER_EXPIRING_SOON -> "لا توجد منتجات تنتهي قريباً"
                        FILTER_CATEGORY -> "لا توجد منتجات في هذه الفئة"
                        else -> "لا توجد منتجات"
                    }
                    binding.emptyStateText.visibility = android.view.View.VISIBLE
                    binding.recyclerView.visibility = android.view.View.GONE
                } else {
                    binding.emptyStateText.visibility = android.view.View.GONE
                    binding.recyclerView.visibility = android.view.View.VISIBLE
                }
            }
        }
    }

    private fun observeProducts() {
        // Additional observations if needed
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
