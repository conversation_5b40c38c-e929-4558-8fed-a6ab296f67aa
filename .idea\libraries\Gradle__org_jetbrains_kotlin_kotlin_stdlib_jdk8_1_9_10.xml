<component name="libraryTable">
  <library name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.jetbrains.kotlin" artifactId="kotlin-stdlib-jdk8" version="1.9.10" baseVersion="1.9.10" />
    <CLASSES>
      <root url="jar://C:/Gradle/gradle-8.4/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.9.10/c7510d64a83411a649c76f2778304ddf71d7437b/kotlin-stdlib-jdk8-1.9.10.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://C:/Gradle/gradle-8.4/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.9.10/5b8f86fea035328fc9e8c660773037a3401ce25f/kotlin-stdlib-jdk8-1.9.10-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://C:/Gradle/gradle-8.4/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.9.10/20739e7aae2a2bfb66c4081b233ffea7b947cf77/kotlin-stdlib-jdk8-1.9.10-sources.jar!/" />
    </SOURCES>
  </library>
</component>