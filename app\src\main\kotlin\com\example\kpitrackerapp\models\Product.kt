package com.example.kpitrackerapp.models

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo
import java.util.Date
import java.util.UUID

@Entity(tableName = "products")
data class Product(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    @ColumnInfo(name = "name") val name: String,
    @ColumnInfo(name = "category") val category: ProductCategory,
    @ColumnInfo(name = "expiry_date") val expiryDate: Date,
    @ColumnInfo(name = "quantity") val quantity: Int = 1,
    @ColumnInfo(name = "location") val location: String? = null,
    @ColumnInfo(name = "notes") val notes: String? = null,
    @ColumnInfo(name = "created_at") val createdAt: Date = Date(),
    @ColumnInfo(name = "updated_at") val updatedAt: Date = Date(),
    @ColumnInfo(name = "is_expired") val isExpired: Boolean = false,
    @ColumnInfo(name = "days_until_expiry") val daysUntilExpiry: Int = 0
)

enum class ProductCategory(val displayName: String, val emoji: String) {
    MEDICINE("Medicine", "💊"),
    MOM_BABY("Mom&baby", "👶"),
    PERSONAL_CARE("Personal care", "🧴"),
    NUTRACEUTICAL("Nutraceutical", "🌿"),
    BEAUTY("Beauty", "💄")
}
