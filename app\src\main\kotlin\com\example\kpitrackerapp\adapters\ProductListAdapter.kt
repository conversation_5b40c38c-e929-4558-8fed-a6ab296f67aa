package com.example.kpitrackerapp.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ItemProductBinding
import com.example.kpitrackerapp.models.Product
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

class ProductListAdapter(
    private val onProductClick: (Product) -> Unit
) : ListAdapter<Product, ProductListAdapter.ProductViewHolder>(ProductDiffCallback()) {

    private val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductViewHolder {
        val binding = ItemProductBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ProductViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ProductViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ProductViewHolder(
        private val binding: ItemProductBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(product: Product) {
            binding.apply {
                // Product info
                productNameText.text = product.name
                categoryText.text = "${product.category.emoji} ${product.category.displayName}"
                quantityText.text = "الكمية: ${product.quantity}"
                locationText.text = "الموقع: ${product.location}"

                // Expiry date
                expiryDateText.text = "تاريخ الانتهاء: ${dateFormat.format(product.expiryDate)}"

                // Days until expiry
                val daysUntilExpiry = product.daysUntilExpiry
                daysText.text = when {
                    daysUntilExpiry < 0 -> "منتهي منذ ${Math.abs(daysUntilExpiry)} يوم"
                    daysUntilExpiry == 0 -> "ينتهي اليوم"
                    daysUntilExpiry == 1 -> "ينتهي غداً"
                    else -> "ينتهي خلال $daysUntilExpiry يوم"
                }

                // Status color
                val statusColor = when {
                    daysUntilExpiry < 0 -> ContextCompat.getColor(root.context, R.color.error_color)
                    daysUntilExpiry <= 7 -> ContextCompat.getColor(root.context, R.color.warning_color)
                    else -> ContextCompat.getColor(root.context, R.color.success_color)
                }

                statusIndicator.setCardBackgroundColor(statusColor)
                daysText.setTextColor(statusColor)

                // Notes (if any)
                if (!product.notes.isNullOrBlank()) {
                    notesText.text = "ملاحظات: ${product.notes}"
                    notesText.visibility = android.view.View.VISIBLE
                } else {
                    notesText.visibility = android.view.View.GONE
                }

                // Click listener
                root.setOnClickListener {
                    onProductClick(product)
                }
            }
        }
    }

    private class ProductDiffCallback : DiffUtil.ItemCallback<Product>() {
        override fun areItemsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem == newItem
        }
    }
}
