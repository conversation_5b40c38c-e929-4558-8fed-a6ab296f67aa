<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.ExpireManagementActivity">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Summary Cards Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp">

                <!-- Total Products Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/totalProductsCard"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:strokeColor="@color/blue_500"
                    app:strokeWidth="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_dashboard_24"
                            android:tint="@color/blue_500"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/totalProductsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/blue_500" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إجمالي المنتجات"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Expired Products Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/expiredProductsCard"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:strokeColor="@color/red_500"
                    app:strokeWidth="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_notification"
                            android:tint="@color/red_500"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/expiredProductsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/red_500" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="منتجات منتهية"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Expiring Soon Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/expiringProductsCard"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:strokeColor="@color/orange_500"
                    app:strokeWidth="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_time"
                            android:tint="@color/orange_500"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/expiringProductsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/orange_500" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تنتهي قريباً"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Categories Section Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="المنتجات حسب الفئة"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="16dp" />

            <!-- Categories Grid -->
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:rowCount="3">

                <!-- Food Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/foodCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_personal">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🍎"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/foodCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="طعام"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Medicine Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/medicineCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_health">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💊"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/medicineCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="أدوية"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Baby & Child Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/babyCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_family">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="👶"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/babyCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="أم وطفل"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Cosmetics Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cosmeticsCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_education">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💄"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/cosmeticsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تجميل"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Supplements Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/supplementsCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_work">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🌿"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/supplementsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="مكملات"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </GridLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addProductFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_check"
        app:tint="@color/white"
        app:backgroundTint="@color/primary_color" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
