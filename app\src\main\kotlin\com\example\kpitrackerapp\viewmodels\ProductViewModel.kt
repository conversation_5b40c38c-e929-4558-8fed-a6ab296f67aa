package com.example.kpitrackerapp.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.kpitrackerapp.models.Product
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.repositories.ProductRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class ProductViewModel(
    private val productRepository: ProductRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProductUiState())
    val uiState: StateFlow<ProductUiState> = _uiState.asStateFlow()

    val allProducts = productRepository.getAllProducts()
    val expiredProducts = productRepository.getExpiredProducts()

    init {
        loadProductCounts()
    }

    fun getProductsByCategory(category: ProductCategory) =
        productRepository.getProductsByCategory(category)

    fun getProductsExpiringInDays(days: Int) =
        productRepository.getProductsExpiringInDays(days)

    fun insertProduct(product: Product) = viewModelScope.launch {
        try {
            _uiState.value = _uiState.value.copy(isLoading = true)
            productRepository.insertProduct(product)
            loadProductCounts()
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                message = "تم إضافة المنتج بنجاح"
            )
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "خطأ في إضافة المنتج: ${e.message}"
            )
        }
    }

    fun updateProduct(product: Product) = viewModelScope.launch {
        try {
            _uiState.value = _uiState.value.copy(isLoading = true)
            productRepository.updateProduct(product)
            loadProductCounts()
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                message = "تم تحديث المنتج بنجاح"
            )
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "خطأ في تحديث المنتج: ${e.message}"
            )
        }
    }

    fun deleteProduct(product: Product) = viewModelScope.launch {
        try {
            _uiState.value = _uiState.value.copy(isLoading = true)
            productRepository.deleteProduct(product)
            loadProductCounts()
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                message = "تم حذف المنتج بنجاح"
            )
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "خطأ في حذف المنتج: ${e.message}"
            )
        }
    }

    private fun loadProductCounts() = viewModelScope.launch {
        try {
            val totalCount = productRepository.getTotalProductsCount()
            val expiredCount = productRepository.getExpiredProductsCount()
            val expiringInWeekCount = productRepository.getProductsExpiringInDaysCount(7)

            val categoryCounts = mutableMapOf<ProductCategory, Int>()
            ProductCategory.values().forEach { category ->
                categoryCounts[category] = productRepository.getProductsCountByCategory(category)
            }

            _uiState.value = _uiState.value.copy(
                totalProductsCount = totalCount,
                expiredProductsCount = expiredCount,
                expiringInWeekCount = expiringInWeekCount,
                categoryCounts = categoryCounts
            )
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "خطأ في تحميل البيانات: ${e.message}"
            )
        }
    }

    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class ProductUiState(
    val isLoading: Boolean = false,
    val totalProductsCount: Int = 0,
    val expiredProductsCount: Int = 0,
    val expiringInWeekCount: Int = 0,
    val categoryCounts: Map<ProductCategory, Int> = emptyMap(),
    val message: String? = null,
    val error: String? = null
)
