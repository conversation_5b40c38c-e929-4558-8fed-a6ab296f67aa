<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_expiry_tracking" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_expiry_tracking.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_expiry_tracking_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="267" endOffset="53"/></Target><Target id="@+id/datePickerCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="31" startOffset="12" endLine="78" endOffset="63"/></Target><Target id="@+id/currentDateText" view="TextView"><Expressions/><location startLine="59" startOffset="20" endLine="67" endOffset="50"/></Target><Target id="@+id/expiredCountText" view="TextView"><Expressions/><location startLine="112" startOffset="24" endLine="120" endOffset="54"/></Target><Target id="@+id/expiringSoonCountText" view="TextView"><Expressions/><location startLine="151" startOffset="24" endLine="159" endOffset="54"/></Target><Target id="@+id/expiryActionCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="168" startOffset="12" endLine="225" endOffset="63"/></Target><Target id="@+id/actionMessageText" view="TextView"><Expressions/><location startLine="206" startOffset="24" endLine="213" endOffset="60"/></Target><Target id="@+id/viewExpiredProductsButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="234" startOffset="16" endLine="245" endOffset="45"/></Target><Target id="@+id/viewExpiringSoonButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="248" startOffset="16" endLine="259" endOffset="69"/></Target></Targets></Layout>