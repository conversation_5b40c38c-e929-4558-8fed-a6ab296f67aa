<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_product" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_add_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/activity_add_product_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="181" endOffset="39"/></Target><Target id="@+id/productNameInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="24" startOffset="12" endLine="30" endOffset="36"/></Target><Target id="@+id/categorySpinner" view="Spinner"><Expressions/><location startLine="43" startOffset="12" endLine="49" endOffset="40"/></Target><Target id="@+id/expiryDateInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="62" startOffset="12" endLine="70" endOffset="41"/></Target><Target id="@+id/quantityInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="91" startOffset="16" endLine="97" endOffset="36"/></Target><Target id="@+id/locationInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="111" startOffset="16" endLine="117" endOffset="41"/></Target><Target id="@+id/notesInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="132" startOffset="12" endLine="139" endOffset="60"/></Target><Target id="@+id/addProductButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="151" startOffset="12" endLine="162" endOffset="65"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="165" startOffset="12" endLine="175" endOffset="80"/></Target></Targets></Layout>