// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityExpireManagementBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton addProductFab;

  @NonNull
  public final MaterialCardView babyCard;

  @NonNull
  public final TextView babyCount;

  @NonNull
  public final MaterialCardView cosmeticsCard;

  @NonNull
  public final TextView cosmeticsCount;

  @NonNull
  public final MaterialCardView expiredProductsCard;

  @NonNull
  public final TextView expiredProductsCount;

  @NonNull
  public final MaterialCardView expiringProductsCard;

  @NonNull
  public final TextView expiringProductsCount;

  @NonNull
  public final MaterialCardView foodCard;

  @NonNull
  public final TextView foodCount;

  @NonNull
  public final MaterialCardView medicineCard;

  @NonNull
  public final TextView medicineCount;

  @NonNull
  public final MaterialCardView supplementsCard;

  @NonNull
  public final TextView supplementsCount;

  @NonNull
  public final MaterialCardView totalProductsCard;

  @NonNull
  public final TextView totalProductsCount;

  private ActivityExpireManagementBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton addProductFab, @NonNull MaterialCardView babyCard,
      @NonNull TextView babyCount, @NonNull MaterialCardView cosmeticsCard,
      @NonNull TextView cosmeticsCount, @NonNull MaterialCardView expiredProductsCard,
      @NonNull TextView expiredProductsCount, @NonNull MaterialCardView expiringProductsCard,
      @NonNull TextView expiringProductsCount, @NonNull MaterialCardView foodCard,
      @NonNull TextView foodCount, @NonNull MaterialCardView medicineCard,
      @NonNull TextView medicineCount, @NonNull MaterialCardView supplementsCard,
      @NonNull TextView supplementsCount, @NonNull MaterialCardView totalProductsCard,
      @NonNull TextView totalProductsCount) {
    this.rootView = rootView;
    this.addProductFab = addProductFab;
    this.babyCard = babyCard;
    this.babyCount = babyCount;
    this.cosmeticsCard = cosmeticsCard;
    this.cosmeticsCount = cosmeticsCount;
    this.expiredProductsCard = expiredProductsCard;
    this.expiredProductsCount = expiredProductsCount;
    this.expiringProductsCard = expiringProductsCard;
    this.expiringProductsCount = expiringProductsCount;
    this.foodCard = foodCard;
    this.foodCount = foodCount;
    this.medicineCard = medicineCard;
    this.medicineCount = medicineCount;
    this.supplementsCard = supplementsCard;
    this.supplementsCount = supplementsCount;
    this.totalProductsCard = totalProductsCard;
    this.totalProductsCount = totalProductsCount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityExpireManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityExpireManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_expire_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityExpireManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addProductFab;
      FloatingActionButton addProductFab = ViewBindings.findChildViewById(rootView, id);
      if (addProductFab == null) {
        break missingId;
      }

      id = R.id.babyCard;
      MaterialCardView babyCard = ViewBindings.findChildViewById(rootView, id);
      if (babyCard == null) {
        break missingId;
      }

      id = R.id.babyCount;
      TextView babyCount = ViewBindings.findChildViewById(rootView, id);
      if (babyCount == null) {
        break missingId;
      }

      id = R.id.cosmeticsCard;
      MaterialCardView cosmeticsCard = ViewBindings.findChildViewById(rootView, id);
      if (cosmeticsCard == null) {
        break missingId;
      }

      id = R.id.cosmeticsCount;
      TextView cosmeticsCount = ViewBindings.findChildViewById(rootView, id);
      if (cosmeticsCount == null) {
        break missingId;
      }

      id = R.id.expiredProductsCard;
      MaterialCardView expiredProductsCard = ViewBindings.findChildViewById(rootView, id);
      if (expiredProductsCard == null) {
        break missingId;
      }

      id = R.id.expiredProductsCount;
      TextView expiredProductsCount = ViewBindings.findChildViewById(rootView, id);
      if (expiredProductsCount == null) {
        break missingId;
      }

      id = R.id.expiringProductsCard;
      MaterialCardView expiringProductsCard = ViewBindings.findChildViewById(rootView, id);
      if (expiringProductsCard == null) {
        break missingId;
      }

      id = R.id.expiringProductsCount;
      TextView expiringProductsCount = ViewBindings.findChildViewById(rootView, id);
      if (expiringProductsCount == null) {
        break missingId;
      }

      id = R.id.foodCard;
      MaterialCardView foodCard = ViewBindings.findChildViewById(rootView, id);
      if (foodCard == null) {
        break missingId;
      }

      id = R.id.foodCount;
      TextView foodCount = ViewBindings.findChildViewById(rootView, id);
      if (foodCount == null) {
        break missingId;
      }

      id = R.id.medicineCard;
      MaterialCardView medicineCard = ViewBindings.findChildViewById(rootView, id);
      if (medicineCard == null) {
        break missingId;
      }

      id = R.id.medicineCount;
      TextView medicineCount = ViewBindings.findChildViewById(rootView, id);
      if (medicineCount == null) {
        break missingId;
      }

      id = R.id.supplementsCard;
      MaterialCardView supplementsCard = ViewBindings.findChildViewById(rootView, id);
      if (supplementsCard == null) {
        break missingId;
      }

      id = R.id.supplementsCount;
      TextView supplementsCount = ViewBindings.findChildViewById(rootView, id);
      if (supplementsCount == null) {
        break missingId;
      }

      id = R.id.totalProductsCard;
      MaterialCardView totalProductsCard = ViewBindings.findChildViewById(rootView, id);
      if (totalProductsCard == null) {
        break missingId;
      }

      id = R.id.totalProductsCount;
      TextView totalProductsCount = ViewBindings.findChildViewById(rootView, id);
      if (totalProductsCount == null) {
        break missingId;
      }

      return new ActivityExpireManagementBinding((CoordinatorLayout) rootView, addProductFab,
          babyCard, babyCount, cosmeticsCard, cosmeticsCount, expiredProductsCard,
          expiredProductsCount, expiringProductsCard, expiringProductsCount, foodCard, foodCount,
          medicineCard, medicineCount, supplementsCard, supplementsCount, totalProductsCard,
          totalProductsCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
