// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityExpireManagementBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton addProductFab;

  @NonNull
  public final MaterialCardView beautyCard;

  @NonNull
  public final TextView beautyCount;

  @NonNull
  public final TextView currentDateText;

  @NonNull
  public final MaterialCardView expiredProductsCard;

  @NonNull
  public final TextView expiredProductsCount;

  @NonNull
  public final MaterialCardView expiringProductsCard;

  @NonNull
  public final TextView expiringProductsCount;

  @NonNull
  public final MaterialCardView expiryActionCard;

  @NonNull
  public final MaterialCardView medicineCard;

  @NonNull
  public final TextView medicineCount;

  @NonNull
  public final MaterialCardView momBabyCard;

  @NonNull
  public final TextView momBabyCount;

  @NonNull
  public final MaterialCardView nutraceuticalCard;

  @NonNull
  public final TextView nutraceuticalCount;

  @NonNull
  public final MaterialButton openExpiryTrackingButton;

  @NonNull
  public final MaterialCardView personalCareCard;

  @NonNull
  public final TextView personalCareCount;

  @NonNull
  public final MaterialCardView totalProductsCard;

  @NonNull
  public final TextView totalProductsCount;

  private ActivityExpireManagementBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton addProductFab, @NonNull MaterialCardView beautyCard,
      @NonNull TextView beautyCount, @NonNull TextView currentDateText,
      @NonNull MaterialCardView expiredProductsCard, @NonNull TextView expiredProductsCount,
      @NonNull MaterialCardView expiringProductsCard, @NonNull TextView expiringProductsCount,
      @NonNull MaterialCardView expiryActionCard, @NonNull MaterialCardView medicineCard,
      @NonNull TextView medicineCount, @NonNull MaterialCardView momBabyCard,
      @NonNull TextView momBabyCount, @NonNull MaterialCardView nutraceuticalCard,
      @NonNull TextView nutraceuticalCount, @NonNull MaterialButton openExpiryTrackingButton,
      @NonNull MaterialCardView personalCareCard, @NonNull TextView personalCareCount,
      @NonNull MaterialCardView totalProductsCard, @NonNull TextView totalProductsCount) {
    this.rootView = rootView;
    this.addProductFab = addProductFab;
    this.beautyCard = beautyCard;
    this.beautyCount = beautyCount;
    this.currentDateText = currentDateText;
    this.expiredProductsCard = expiredProductsCard;
    this.expiredProductsCount = expiredProductsCount;
    this.expiringProductsCard = expiringProductsCard;
    this.expiringProductsCount = expiringProductsCount;
    this.expiryActionCard = expiryActionCard;
    this.medicineCard = medicineCard;
    this.medicineCount = medicineCount;
    this.momBabyCard = momBabyCard;
    this.momBabyCount = momBabyCount;
    this.nutraceuticalCard = nutraceuticalCard;
    this.nutraceuticalCount = nutraceuticalCount;
    this.openExpiryTrackingButton = openExpiryTrackingButton;
    this.personalCareCard = personalCareCard;
    this.personalCareCount = personalCareCount;
    this.totalProductsCard = totalProductsCard;
    this.totalProductsCount = totalProductsCount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityExpireManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityExpireManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_expire_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityExpireManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addProductFab;
      FloatingActionButton addProductFab = ViewBindings.findChildViewById(rootView, id);
      if (addProductFab == null) {
        break missingId;
      }

      id = R.id.beautyCard;
      MaterialCardView beautyCard = ViewBindings.findChildViewById(rootView, id);
      if (beautyCard == null) {
        break missingId;
      }

      id = R.id.beautyCount;
      TextView beautyCount = ViewBindings.findChildViewById(rootView, id);
      if (beautyCount == null) {
        break missingId;
      }

      id = R.id.currentDateText;
      TextView currentDateText = ViewBindings.findChildViewById(rootView, id);
      if (currentDateText == null) {
        break missingId;
      }

      id = R.id.expiredProductsCard;
      MaterialCardView expiredProductsCard = ViewBindings.findChildViewById(rootView, id);
      if (expiredProductsCard == null) {
        break missingId;
      }

      id = R.id.expiredProductsCount;
      TextView expiredProductsCount = ViewBindings.findChildViewById(rootView, id);
      if (expiredProductsCount == null) {
        break missingId;
      }

      id = R.id.expiringProductsCard;
      MaterialCardView expiringProductsCard = ViewBindings.findChildViewById(rootView, id);
      if (expiringProductsCard == null) {
        break missingId;
      }

      id = R.id.expiringProductsCount;
      TextView expiringProductsCount = ViewBindings.findChildViewById(rootView, id);
      if (expiringProductsCount == null) {
        break missingId;
      }

      id = R.id.expiryActionCard;
      MaterialCardView expiryActionCard = ViewBindings.findChildViewById(rootView, id);
      if (expiryActionCard == null) {
        break missingId;
      }

      id = R.id.medicineCard;
      MaterialCardView medicineCard = ViewBindings.findChildViewById(rootView, id);
      if (medicineCard == null) {
        break missingId;
      }

      id = R.id.medicineCount;
      TextView medicineCount = ViewBindings.findChildViewById(rootView, id);
      if (medicineCount == null) {
        break missingId;
      }

      id = R.id.momBabyCard;
      MaterialCardView momBabyCard = ViewBindings.findChildViewById(rootView, id);
      if (momBabyCard == null) {
        break missingId;
      }

      id = R.id.momBabyCount;
      TextView momBabyCount = ViewBindings.findChildViewById(rootView, id);
      if (momBabyCount == null) {
        break missingId;
      }

      id = R.id.nutraceuticalCard;
      MaterialCardView nutraceuticalCard = ViewBindings.findChildViewById(rootView, id);
      if (nutraceuticalCard == null) {
        break missingId;
      }

      id = R.id.nutraceuticalCount;
      TextView nutraceuticalCount = ViewBindings.findChildViewById(rootView, id);
      if (nutraceuticalCount == null) {
        break missingId;
      }

      id = R.id.openExpiryTrackingButton;
      MaterialButton openExpiryTrackingButton = ViewBindings.findChildViewById(rootView, id);
      if (openExpiryTrackingButton == null) {
        break missingId;
      }

      id = R.id.personalCareCard;
      MaterialCardView personalCareCard = ViewBindings.findChildViewById(rootView, id);
      if (personalCareCard == null) {
        break missingId;
      }

      id = R.id.personalCareCount;
      TextView personalCareCount = ViewBindings.findChildViewById(rootView, id);
      if (personalCareCount == null) {
        break missingId;
      }

      id = R.id.totalProductsCard;
      MaterialCardView totalProductsCard = ViewBindings.findChildViewById(rootView, id);
      if (totalProductsCard == null) {
        break missingId;
      }

      id = R.id.totalProductsCount;
      TextView totalProductsCount = ViewBindings.findChildViewById(rootView, id);
      if (totalProductsCount == null) {
        break missingId;
      }

      return new ActivityExpireManagementBinding((CoordinatorLayout) rootView, addProductFab,
          beautyCard, beautyCount, currentDateText, expiredProductsCard, expiredProductsCount,
          expiringProductsCard, expiringProductsCount, expiryActionCard, medicineCard,
          medicineCount, momBabyCard, momBabyCount, nutraceuticalCard, nutraceuticalCount,
          openExpiryTrackingButton, personalCareCard, personalCareCount, totalProductsCard,
          totalProductsCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
