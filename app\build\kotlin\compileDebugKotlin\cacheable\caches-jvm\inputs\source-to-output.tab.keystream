Iapp/src/main/kotlin/com/example/kpitrackerapp/utils/AppDetectionHelper.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserKpiListActivity.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/CompactReportAdapter.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/EnhancedTaskAdapter.ktHapp/src/main/kotlin/com/example/kpitrackerapp/utils/AutoSendScheduler.kt?app/src/main/kotlin/com/example/kpitrackerapp/models/Subtask.ktCapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryItem.ktJapp/src/main/kotlin/com/example/kpitrackerapp/utils/CardAnimationHelper.ktIapp/src/main/kotlin/com/example/kpitrackerapp/models/UserKpiAssignment.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/NotificationsActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/RecentUsersAdapter.ktHapp/src/main/kotlin/com/example/kpitrackerapp/models/KpiProgressEntry.ktCapp/src/main/kotlin/com/example/kpitrackerapp/persistence/KpiDao.ktPapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ChatViewModelFactory.ktNapp/src/main/kotlin/com/example/kpitrackerapp/ui/SearchEditProgressActivity.ktHapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModel.ktGapp/src/main/kotlin/com/example/kpitrackerapp/persistence/Converters.ktKapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserFilterAdapter.kt@app/src/main/kotlin/com/example/kpitrackerapp/ui/ChatActivity.ktKapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiProgressEntryAdapter.ktOapp/src/main/kotlin/com/example/kpitrackerapp/workers/AutoReportSenderWorker.ktDapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserFilterDialog.kt;app/src/main/kotlin/com/example/kpitrackerapp/models/Kpi.ktLapp/src/main/kotlin/com/example/kpitrackerapp/ui/AutoSendSettingsActivity.kt<app/src/main/kotlin/com/example/kpitrackerapp/models/User.ktEapp/src/main/kotlin/com/example/kpitrackerapp/models/OcrResultItem.ktDapp/src/main/kotlin/com/example/kpitrackerapp/persistence/ChatDao.ktPapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReminderSettingsActivity.ktCapp/src/main/kotlin/com/example/kpitrackerapp/ui/ChartMarkerView.ktPapp/src/main/kotlin/com/example/kpitrackerapp/persistence/KpiProgressEntryDao.ktMapp/src/main/kotlin/com/example/kpitrackerapp/utils/FirebaseMessageManager.ktLapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ChatMessageAdapter.ktOapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditAdvancedTaskActivity.ktEapp/src/main/kotlin/com/example/kpitrackerapp/utils/SessionManager.ktLapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExpireManagementActivity.ktDapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewAdapter.ktLapp/src/main/kotlin/com/example/kpitrackerapp/repositories/ChatRepository.ktPapp/src/main/kotlin/com/example/kpitrackerapp/fragments/MainDashboardFragment.ktLapp/src/main/kotlin/com/example/kpitrackerapp/workers/RecurringTaskWorker.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelImportActivity.ktAapp/src/main/kotlin/com/example/kpitrackerapp/ui/LoginActivity.ktJapp/src/main/kotlin/com/example/kpitrackerapp/models/AdminDashboardData.ktIapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/TaskViewModel.ktLapp/src/main/kotlin/com/example/kpitrackerapp/persistence/TaskCategoryDao.ktBapp/src/main/kotlin/com/example/kpitrackerapp/models/ChatModels.ktLapp/src/main/kotlin/com/example/kpitrackerapp/fragments/DashboardFragment.kt<app/src/main/kotlin/com/example/kpitrackerapp/models/Task.ktDapp/src/main/kotlin/com/example/kpitrackerapp/persistence/UserDao.ktDapp/src/main/kotlin/com/example/kpitrackerapp/ui/ChatListActivity.ktEapp/src/main/kotlin/com/example/kpitrackerapp/models/SmartListItem.ktDapp/src/main/kotlin/com/example/kpitrackerapp/models/TaskCategory.ktHapp/src/main/kotlin/com/example/kpitrackerapp/utils/CardGestureHelper.ktPapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/TaskViewModelFactory.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/ModernReportActivity.ktJapp/src/main/kotlin/com/example/kpitrackerapp/models/AdminDashboardItem.ktKapp/src/main/kotlin/com/example/kpitrackerapp/fragments/MessagesFragment.ktAapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportAdapter.ktKapp/src/main/kotlin/com/example/kpitrackerapp/utils/RecurringTaskManager.ktMapp/src/main/kotlin/com/example/kpitrackerapp/utils/EisenhowerMatrixHelper.ktQapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditProgressDialogFragment.ktJapp/src/main/kotlin/com/example/kpitrackerapp/fragments/AccountFragment.kt?app/src/main/kotlin/com/example/kpitrackerapp/ui/OcrActivity.ktQapp/src/main/kotlin/com/example/kpitrackerapp/workers/ExpiryNotificationWorker.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewActivity.ktNapp/src/main/kotlin/com/example/kpitrackerapp/fragments/PerformanceFragment.kt=app/src/main/kotlin/com/example/kpitrackerapp/MainActivity.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/ColoredReportAdapter.ktNapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiOriginalActivity.ktOapp/src/main/kotlin/com/example/kpitrackerapp/adapters/AdminDashboardAdapter.ktBapp/src/main/kotlin/com/example/kpitrackerapp/ui/ReportActivity.ktDapp/src/main/kotlin/com/example/kpitrackerapp/util/ExcelDataCache.ktDapp/src/main/kotlin/com/example/kpitrackerapp/persistence/TaskDao.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExcelReviewAdapter.ktFapp/src/main/kotlin/com/example/kpitrackerapp/KpiTrackerApplication.ktHapp/src/main/kotlin/com/example/kpitrackerapp/ui/UnifiedReportAdapter.ktEapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportAdapter.ktBapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiListAdapter.ktGapp/src/main/kotlin/com/example/kpitrackerapp/AdminDashboardActivity.ktGapp/src/main/kotlin/com/example/kpitrackerapp/persistence/SubtaskDao.ktQapp/src/main/kotlin/com/example/kpitrackerapp/persistence/UserKpiAssignmentDao.ktJapp/src/main/kotlin/com/example/kpitrackerapp/utils/NotificationManager.ktMapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ConversationAdapter.ktOapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiViewModelFactory.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddEditKpiActivity.kt?app/src/main/kotlin/com/example/kpitrackerapp/ui/TaskAdapter.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/UserSummaryAdapter.ktKapp/src/main/kotlin/com/example/kpitrackerapp/workers/TaskReminderWorker.ktHapp/src/main/kotlin/com/example/kpitrackerapp/persistence/AppDatabase.ktRapp/src/main/kotlin/com/example/kpitrackerapp/services/FirebaseMessagingService.ktHapp/src/main/kotlin/com/example/kpitrackerapp/util/NotificationHelper.ktEapp/src/main/kotlin/com/example/kpitrackerapp/ui/KpiDetailActivity.ktKapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/KpiWithProgress.ktFapp/src/main/kotlin/com/example/kpitrackerapp/utils/LanguageManager.ktEapp/src/main/kotlin/com/example/kpitrackerapp/models/TaskAnalytics.ktIapp/src/main/kotlin/com/example/kpitrackerapp/adapters/UserListAdapter.ktEapp/src/main/kotlin/com/example/kpitrackerapp/utils/DragDropHelper.ktJapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskManagementActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/models/UserFilterItem.ktEapp/src/main/kotlin/com/example/kpitrackerapp/ui/OcrReviewActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/TaskReportActivity.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/CreateUserActivity.ktCapp/src/main/kotlin/com/example/kpitrackerapp/utils/ThemeManager.ktIapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ChatViewModel.ktMapp/src/main/kotlin/com/example/kpitrackerapp/adapters/NotificationAdapter.ktHapp/src/main/kotlin/com/example/kpitrackerapp/utils/AlarmClockManager.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/ModernAddTaskActivity.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/PomodoroTimerActivity.ktNapp/src/main/kotlin/com/example/kpitrackerapp/adapters/SearchResultsAdapter.ktDapp/src/main/kotlin/com/example/kpitrackerapp/utils/HijriCalendar.ktIapp/src/main/kotlin/com/example/kpitrackerapp/ui/DateConverterActivity.ktLapp/src/main/kotlin/com/example/kpitrackerapp/fragments/DrugIndexFragment.kt?app/src/main/kotlin/com/example/kpitrackerapp/models/Product.ktLapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ProductViewModel.ktFapp/src/main/kotlin/com/example/kpitrackerapp/ui/AddProductActivity.ktGapp/src/main/kotlin/com/example/kpitrackerapp/persistence/ProductDao.ktOapp/src/main/kotlin/com/example/kpitrackerapp/repositories/ProductRepository.ktSapp/src/main/kotlin/com/example/kpitrackerapp/viewmodels/ProductViewModelFactory.ktJapp/src/main/kotlin/com/example/kpitrackerapp/ui/ExpiryTrackingActivity.ktLapp/src/main/kotlin/com/example/kpitrackerapp/adapters/ProductListAdapter.ktGapp/src/main/kotlin/com/example/kpitrackerapp/ui/ProductListActivity.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         