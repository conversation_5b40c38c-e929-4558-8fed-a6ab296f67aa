<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="light_gray">#D3D3D3</color>
    <color name="dark_gray">#A9A9A9</color>
    <color name="red">#FFFF0000</color>
    <color name="green">#FF00FF00</color> <!-- Original Green -->
    <color name="blue">#FF0000FF</color>
    <color name="yellow">#FFFFFF00</color>
    <color name="orange">#FFFFA500</color> <!-- Original Orange -->
    <color name="light_blue_50">#FFE1F5FE</color>
    <color name="light_blue_200">#FF81D4FA</color>
    <color name="light_blue_600">#FF039BE5</color>
    <color name="light_blue_900">#FF01579B</color>
    <color name="summary_card_top_red">#FFEF9A9A</color> <!-- Example: Light Red -->
    <color name="summary_card_bottom_yellow">#FFFFF59D</color> <!-- Example: Light Yellow -->
    <color name="chart_target_color">#FFEF5350</color> <!-- A shade of Red -->
    <color name="chart_achieved_color">#FF42A5F5</color> <!-- A shade of Blue -->
    <color name="default_kpi_card_color">#FFFFFFFF</color> <!-- Default White -->
    <color name="default_kpi_card_top_gradient">#FF6200EE</color> <!-- Default Purple 500 -->
    <color name="default_kpi_card_bottom_gradient">#FF3700B3</color> <!-- Default Purple 700 -->

    <!-- Added for resolving build errors (Set 1) -->
    <color name="chart_fill_purple_light">#406200EE</color> <!-- Example: Light purple with alpha -->
    <color name="header_gradient_start">#FF6200EE</color> <!-- Example: Purple 500 -->
    <color name="header_gradient_end">#FF3700B3</color> <!-- Example: Purple 700 -->
    <color name="kpi_concern">#FFFF6F00</color> <!-- Example: Amber/Orange -->
    <color name="kpi_good">#FF4CAF50</color> <!-- Example: Green -->
    <color name="status_green">#FF4CAF50</color> <!-- Example: Green -->
    <color name="status_orange">#FFFF9800</color> <!-- Example: Orange -->
    <color name="status_red">#FFF44336</color> <!-- Example: Red -->
    <color name="soft_lavender_background">#FFE6E6FA</color> <!-- Example: Lavender -->
    <color name="purple_accent">#FF9C27B0</color> <!-- Example: Purple Accent -->
    <color name="purple_button_text">#FFFFFFFF</color> <!-- Example: White -->
    <color name="screen_background_light_blue">#FFE3F2FD</color> <!-- Example: Light Blue -->
    <color name="progress_indicator_track">#FFBDBDBD</color> <!-- Example: Grey for track -->
    <color name="progress_indicator_blue">#FF2196F3</color> <!-- Example: Blue for progress -->

    <!-- Adding missing colors identified in build log -->
    <color name="fab_color">#FF6200EE</color> <!-- Referenced in activity_main.xml -->
    <color name="chip_background_default">#FFE0E0E0</color> <!-- Referenced in kpi_card_item.xml -->
    <color name="progress_track_color">#FFBDBDBD</color> <!-- Referenced in kpi_card_item.xml -->
    <color name="progress_color_default">#FF6200EE</color> <!-- Referenced in kpi_card_item.xml -->
    <color name="summary_card_image_tint">#FFFFFFFF</color> <!-- Referenced in overall_summary_card_item.xml & user_summary_card_item.xml -->
    <color name="summary_card_image_background">#00FFFFFF</color> <!-- Referenced in overall_summary_card_item.xml & user_summary_card_item.xml -->
    <color name="summary_card_username_text">#FFFFFFFF</color> <!-- Referenced in user_summary_card_item.xml -->

    <!-- Added for resolving Kotlin build errors (Set 2) -->
    <color name="light_red">#FFEF9A9A</color> <!-- Example: Light Red -->
    <color name="dark_red">#FFD32F2F</color> <!-- Example: Dark Red -->
    <color name="light_green">#FFA5D6A7</color> <!-- Example: Light Green -->
    <color name="dark_green">#FF388E3C</color> <!-- Example: Dark Green -->
    <color name="light_yellow">#FFFFF59D</color> <!-- Example: Light Yellow -->
    <color name="dark_yellow">#FFFBC02D</color> <!-- Example: Dark Yellow -->
    <color name="chart_line_purple">#FFAB47BC</color> <!-- Example: Purple Accent -->
    <color name="card_background_default">#FFFFFFFF</color> <!-- Example: White -->
    <color name="kpi_warning">#FFFFC107</color> <!-- Example: Amber -->

    <!-- Doctor-specific colors for report tables -->
    <color name="doctor_color_1">#673AB7</color> <!-- Purple -->
    <color name="doctor_color_2">#2196F3</color> <!-- Blue -->
    <color name="doctor_color_3">#4CAF50</color> <!-- Green -->
    <color name="doctor_color_4">#FF9800</color> <!-- Orange -->
    <color name="doctor_color_5">#E91E63</color> <!-- Pink -->

    <!-- Light versions for table row backgrounds -->
    <color name="doctor_color_1_light">#EDE7F6</color> <!-- Light Purple -->
    <color name="doctor_color_2_light">#E3F2FD</color> <!-- Light Blue -->
    <color name="doctor_color_3_light">#E8F5E9</color> <!-- Light Green -->
    <color name="doctor_color_4_light">#FFF3E0</color> <!-- Light Orange -->
    <color name="doctor_color_5_light">#FCE4EC</color> <!-- Light Pink -->

    <!-- Even lighter versions for alternating rows -->
    <color name="doctor_color_1_lighter">#F3F0F9</color> <!-- Lighter Purple -->
    <color name="doctor_color_2_lighter">#EFF8FE</color> <!-- Lighter Blue -->
    <color name="doctor_color_3_lighter">#F1FAF1</color> <!-- Lighter Green -->
    <color name="doctor_color_4_lighter">#FFF8ED</color> <!-- Lighter Orange -->
    <color name="doctor_color_5_lighter">#FDF2F6</color> <!-- Lighter Pink -->

    <!-- Performance indicator colors -->
    <color name="performance_excellent">#4CAF50</color> <!-- Green for high performance (>= 90%) -->
    <color name="performance_good">#8BC34A</color> <!-- Light Green for good performance (>= 75%) -->
    <color name="performance_average">#FFC107</color> <!-- Yellow for average performance (>= 50%) -->
    <color name="performance_below_average">#FF9800</color> <!-- Orange for below average (>= 25%) -->
    <color name="performance_poor">#F44336</color> <!-- Red for poor performance (< 25%) -->

    <!-- Login and UI colors -->
    <color name="gray_200">#FFEEEEEE</color>
    <color name="gray_300">#FFE0E0E0</color>
    <color name="gray_500">#FF9E9E9E</color>
    <color name="gray_600">#FF757575</color>
    <color name="gray_700">#FF616161</color>
    <color name="gray_800">#FF424242</color>

    <!-- Permission indicator colors -->
    <color name="owner_card_border">#FF6200EE</color>
    <color name="read_only_overlay">#40000000</color>
    <color name="edit_indicator">#FF4CAF50</color>
    <color name="view_only_indicator">#FFFF9800</color>

    <!-- Admin Dashboard colors -->
    <color name="background_light">#FFF5F5F5</color>
    <color name="text_primary">#FF212121</color>
    <color name="text_secondary">#FF757575</color>
    <color name="primary_dark">#FF3700B3</color>

    <!-- Chat colors -->
    <color name="chat_background">#FFF5F5F5</color>
    <color name="sent_message_background">#FF6200EE</color>
    <color name="received_message_background">#FFFFFFFF</color>
    <color name="message_input_background">#FFFFFFFF</color>
    <color name="online_color">#FF4CAF50</color>
    <color name="unread_badge_color">#FFF44336</color>
    <color name="date_header_background">#40000000</color>
    <color name="system_message_background">#40757575</color>
    <color name="reply_background">#20000000</color>
    <color name="message_read_color">#FF4CAF50</color>
    <color name="kpi_share_background">#FF2196F3</color>
    <color name="progress_share_background">#FF4CAF50</color>

    <!-- User role colors -->
    <color name="super_admin_color">#FFE91E63</color>
    <color name="admin_color">#FF9C27B0</color>
    <color name="user_color">#FF607D8B</color>

    <!-- Notification colors -->
    <color name="notification_admin_bg">#FFF3E5F5</color>
    <color name="notification_kpi_bg">#FFE8F5E8</color>
    <color name="notification_reminder_bg">#FFFEF7E0</color>
    <color name="notification_system_bg">#FFE3F2FD</color>
    <color name="background">#FFF5F5F5</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="text_hint">#FF9E9E9E</color>

    <!-- WhatsApp and social media colors -->
    <color name="whatsapp_green">#FF25D366</color>
    <color name="background_color">#FFF5F5F5</color>
    <color name="primary_color">#FF6200EE</color>

    <!-- Enhanced Task System Colors -->
    <!-- Priority Colors -->
    <color name="priority_low">#FF4CAF50</color>
    <color name="priority_medium">#FFFF9800</color>
    <color name="priority_high">#FFFF5722</color>
    <color name="priority_urgent">#FFF44336</color>

    <!-- Importance Colors -->
    <color name="importance_low">#FF9E9E9E</color>
    <color name="importance_medium">#FF2196F3</color>
    <color name="importance_high">#FF9C27B0</color>
    <color name="importance_critical">#FFE91E63</color>

    <!-- Task Status Colors -->
    <color name="today_color">#FF2196F3</color>
    <color name="urgent_color">#FFF44336</color>
    <color name="success_color">#FF4CAF50</color>
    <color name="overdue_color">#FFF44336</color>

    <!-- Eisenhower Matrix Colors -->
    <color name="urgent_important_bg">#FFD32F2F</color>
    <color name="not_urgent_important_bg">#FF1976D2</color>
    <color name="urgent_not_important_bg">#FFFF9800</color>
    <color name="not_urgent_not_important_bg">#FF757575</color>

    <!-- Progress Colors -->
    <color name="progress_tint">#FF4CAF50</color>
    <color name="progress_background">#FFE0E0E0</color>
    <color name="progress_text">#FF4CAF50</color>

    <!-- Chip Colors -->
    <color name="urgent_chip_background">#FFFFE0E0</color>
    <color name="overdue_chip_background">#FFFFE0E0</color>

    <!-- Category Colors -->
    <color name="category_work">#FF2196F3</color>
    <color name="category_personal">#FF4CAF50</color>
    <color name="category_health">#FFFF9800</color>
    <color name="category_education">#FF9C27B0</color>
    <color name="category_finance">#FFF44336</color>
    <color name="category_family">#FFE91E63</color>
    <color name="category_shopping">#FFFF5722</color>
    <color name="category_travel">#FF00BCD4</color>

    <!-- UI Enhancement Colors -->
    <color name="matrix_toggle_background">#FFF0F0F0</color>
    <color name="icon_tint">#FF757575</color>
    <color name="search_background">#FFFFFFFF</color>

    <!-- Energy Level Colors -->
    <color name="energy_low">#FFFF9800</color>
    <color name="energy_medium">#FF2196F3</color>
    <color name="energy_high">#FF4CAF50</color>

    <!-- Advanced Task Colors -->
    <color name="ai_suggestion_background">#FFF3E5F5</color>
    <color name="task_color_red">#FFF44336</color>
    <color name="task_color_blue">#FF2196F3</color>
    <color name="task_color_green">#FF4CAF50</color>
    <color name="task_color_orange">#FFFF9800</color>
    <color name="task_color_purple">#FF9C27B0</color>

    <!-- Priority-based Colors -->
    <color name="medium_priority_color">#FFFF9800</color>
    <color name="low_priority_color">#FF4CAF50</color>

    <!-- Additional Material Colors -->
    <color name="blue_500">#FF2196F3</color>
    <color name="green_500">#FF4CAF50</color>
    <color name="red_500">#FFF44336</color>
    <color name="orange_500">#FFFF9800</color>

    <!-- Search and Highlight Colors -->
    <color name="highlight_yellow">#FFFFFF00</color>

    <!-- Product Management Colors -->
    <color name="error_color">#FFF44336</color>
    <color name="error_light">#FFFFEBEE</color>
    <color name="warning_color">#FFFF9800</color>
    <color name="warning_light">#FFFFF3E0</color>

</resources>
