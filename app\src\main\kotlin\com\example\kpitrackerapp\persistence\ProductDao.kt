package com.example.kpitrackerapp.persistence

import androidx.room.*
import com.example.kpitrackerapp.models.Product
import com.example.kpitrackerapp.models.ProductCategory
import kotlinx.coroutines.flow.Flow
import java.util.Date

@Dao
interface ProductDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: Product): Long
    
    @Update
    suspend fun updateProduct(product: Product)
    
    @Delete
    suspend fun deleteProduct(product: Product)
    
    @Query("SELECT * FROM products ORDER BY expiry_date ASC")
    fun getAllProducts(): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE id = :productId")
    fun getProductById(productId: String): Flow<Product?>
    
    @Query("SELECT * FROM products WHERE category = :category ORDER BY expiry_date ASC")
    fun getProductsByCategory(category: ProductCategory): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE expiry_date < :currentDate")
    fun getExpiredProducts(currentDate: Date): Flow<List<Product>>
    
    @Query("SELECT * FROM products WHERE expiry_date BETWEEN :startDate AND :endDate ORDER BY expiry_date ASC")
    fun getProductsExpiringInRange(startDate: Date, endDate: Date): Flow<List<Product>>
    
    @Query("SELECT COUNT(*) FROM products")
    suspend fun getTotalProductsCount(): Int
    
    @Query("SELECT COUNT(*) FROM products WHERE expiry_date < :currentDate")
    suspend fun getExpiredProductsCount(currentDate: Date): Int
    
    @Query("SELECT COUNT(*) FROM products WHERE expiry_date BETWEEN :startDate AND :endDate")
    suspend fun getProductsExpiringInRangeCount(startDate: Date, endDate: Date): Int
    
    @Query("SELECT COUNT(*) FROM products WHERE category = :category")
    suspend fun getProductsCountByCategory(category: ProductCategory): Int
    
    @Query("DELETE FROM products WHERE expiry_date < :cutoffDate")
    suspend fun deleteExpiredProductsOlderThan(cutoffDate: Date): Int
}
