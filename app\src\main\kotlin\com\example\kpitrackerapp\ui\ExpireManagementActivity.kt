package com.example.kpitrackerapp.ui

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityExpireManagementBinding
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.viewmodels.ProductViewModel
import com.example.kpitrackerapp.viewmodels.ProductViewModelFactory
import com.example.kpitrackerapp.persistence.AppDatabase
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class ExpireManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityExpireManagementBinding
    private val productViewModel: ProductViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        ProductViewModelFactory(
            application,
            database.productDao()
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityExpireManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupActionBar()
        setupClickListeners()
        setupCurrentDate()
        observeViewModel()
    }

    private fun setupActionBar() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = "لوحة الحكم"
    }

    private fun setupClickListeners() {
        // إجمالي المنتجات
        binding.totalProductsCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_ALL)
        }

        // منتجات منتهية
        binding.expiredProductsCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_EXPIRED)
        }

        // تنتهي قريباً
        binding.expiringProductsCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_EXPIRING_SOON)
        }

        // Category cards
        binding.medicineCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_CATEGORY, ProductCategory.MEDICINE)
        }

        binding.momBabyCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_CATEGORY, ProductCategory.MOM_BABY)
        }

        binding.personalCareCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_CATEGORY, ProductCategory.PERSONAL_CARE)
        }

        binding.nutraceuticalCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_CATEGORY, ProductCategory.NUTRACEUTICAL)
        }

        binding.beautyCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_CATEGORY, ProductCategory.BEAUTY)
        }

        // Add new product button
        binding.addProductFab.setOnClickListener {
            startActivity(Intent(this, AddProductActivity::class.java))
        }

        // Expiry action card click
        binding.expiryActionCard.setOnClickListener {
            openProductList(ProductListActivity.FILTER_EXPIRED)
        }
    }

    private fun setupCurrentDate() {
        val dateFormat = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
        val currentDate = dateFormat.format(Date())
        binding.currentDateText.text = "التاريخ المحدد: $currentDate"
    }

    private fun openProductList(filterType: String, category: ProductCategory? = null) {
        val intent = Intent(this, ProductListActivity::class.java).apply {
            putExtra(ProductListActivity.EXTRA_FILTER_TYPE, filterType)
            category?.let {
                putExtra(ProductListActivity.EXTRA_CATEGORY, it.name)
            }
        }
        startActivity(intent)
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            productViewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
    }

    private fun updateUI(state: com.example.kpitrackerapp.viewmodels.ProductUiState) {
        // Update total products count
        binding.totalProductsCount.text = state.totalProductsCount.toString()

        // Update expired products count
        binding.expiredProductsCount.text = state.expiredProductsCount.toString()

        // Update expiring products count
        binding.expiringProductsCount.text = state.expiringInWeekCount.toString()

        // Update category counts
        state.categoryCounts.forEach { (category, count) ->
            when (category) {
                ProductCategory.MEDICINE -> binding.medicineCount.text = count.toString()
                ProductCategory.MOM_BABY -> binding.momBabyCount.text = count.toString()
                ProductCategory.PERSONAL_CARE -> binding.personalCareCount.text = count.toString()
                ProductCategory.NUTRACEUTICAL -> binding.nutraceuticalCount.text = count.toString()
                ProductCategory.BEAUTY -> binding.beautyCount.text = count.toString()
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish() // Go back to the previous activity
        return true
    }
}
