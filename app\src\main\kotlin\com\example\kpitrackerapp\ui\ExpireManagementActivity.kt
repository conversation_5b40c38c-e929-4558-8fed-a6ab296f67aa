package com.example.kpitrackerapp.ui

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityExpireManagementBinding
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.viewmodels.ProductViewModel
import com.example.kpitrackerapp.viewmodels.ProductViewModelFactory
import com.example.kpitrackerapp.persistence.AppDatabase
import kotlinx.coroutines.launch

class ExpireManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityExpireManagementBinding
    private val productViewModel: ProductViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        ProductViewModelFactory(
            application,
            database.productDao()
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityExpireManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupActionBar()
        setupClickListeners()
        observeViewModel()
    }

    private fun setupActionBar() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = "لوحة الحكم"
    }

    private fun setupClickListeners() {
        // إجمالي المنتجات
        binding.totalProductsCard.setOnClickListener {
            // Navigate to all products list
        }

        // منتجات منتهية
        binding.expiredProductsCard.setOnClickListener {
            // Navigate to expired products list
        }

        // تنتهي قريباً
        binding.expiringProductsCard.setOnClickListener {
            // Navigate to expiring products list
        }

        // Category cards
        binding.foodCard.setOnClickListener {
            // Navigate to food products
        }

        binding.medicineCard.setOnClickListener {
            // Navigate to medicine products
        }

        binding.babyCard.setOnClickListener {
            // Navigate to baby products
        }

        binding.cosmeticsCard.setOnClickListener {
            // Navigate to cosmetics products
        }

        binding.supplementsCard.setOnClickListener {
            // Navigate to supplements products
        }

        // Add new product button
        binding.addProductFab.setOnClickListener {
            startActivity(Intent(this, AddProductActivity::class.java))
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            productViewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
    }

    private fun updateUI(state: com.example.kpitrackerapp.viewmodels.ProductUiState) {
        // Update total products count
        binding.totalProductsCount.text = state.totalProductsCount.toString()

        // Update expired products count
        binding.expiredProductsCount.text = state.expiredProductsCount.toString()

        // Update expiring products count
        binding.expiringProductsCount.text = state.expiringInWeekCount.toString()

        // Update category counts
        state.categoryCounts.forEach { (category, count) ->
            when (category) {
                ProductCategory.FOOD -> binding.foodCount.text = count.toString()
                ProductCategory.MEDICINE -> binding.medicineCount.text = count.toString()
                ProductCategory.BABY_CHILD -> binding.babyCount.text = count.toString()
                ProductCategory.COSMETICS -> binding.cosmeticsCount.text = count.toString()
                ProductCategory.SUPPLEMENTS -> binding.supplementsCount.text = count.toString()
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish() // Go back to the previous activity
        return true
    }
}
