package com.example.kpitrackerapp.persistence

import android.content.Context // Keep one
import androidx.room.Database // Keep one
import androidx.room.Room // Keep one
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration // Keep one
import androidx.sqlite.db.SupportSQLiteDatabase // Import SupportSQLiteDatabase
import com.example.kpitrackerapp.models.Kpi
import com.example.kpitrackerapp.models.KpiProgressEntry
import com.example.kpitrackerapp.models.User // Added import
import com.example.kpitrackerapp.models.UserKpiAssignment // Added import
import com.example.kpitrackerapp.models.Task // Import Task entity
import com.example.kpitrackerapp.models.TaskCategory // Import TaskCategory entity
import com.example.kpitrackerapp.models.Subtask // Import Subtask entity
import com.example.kpitrackerapp.models.ChatMessage // Import Chat entities
import com.example.kpitrackerapp.models.Conversation
import com.example.kpitrackerapp.models.Product // Import Product entity

// Increment version number to 34 for product category migration
@Database(entities = [Kpi::class, KpiProgressEntry::class, User::class, UserKpiAssignment::class, Task::class, TaskCategory::class, Subtask::class, ChatMessage::class, Conversation::class, Product::class], version = 34, exportSchema = false)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {

    abstract fun kpiDao(): KpiDao
    abstract fun progressEntryDao(): KpiProgressEntryDao // Add DAO for progress entries
    abstract fun userDao(): UserDao // Added User DAO accessor
    abstract fun userKpiAssignmentDao(): UserKpiAssignmentDao // Added Assignment DAO accessor
    abstract fun taskDao(): TaskDao // Added Task DAO accessor
    abstract fun taskCategoryDao(): TaskCategoryDao // Added TaskCategory DAO accessor
    abstract fun subtaskDao(): SubtaskDao // Added Subtask DAO accessor
    abstract fun chatDao(): ChatDao // Added Chat DAO accessor
    abstract fun productDao(): ProductDao // Added Product DAO accessor

    companion object {
        // Migration from 22 to 23: Add reminderDaysBefore to tasks table
        val MIGRATION_22_23 = object : Migration(22, 23) {
            override fun migrate(db: SupportSQLiteDatabase) {
                db.execSQL("ALTER TABLE tasks ADD COLUMN reminderDaysBefore INTEGER")
            }
        }

        // Migration from 23 to 24: Add login fields to users table
        val MIGRATION_23_24 = object : Migration(23, 24) {
            override fun migrate(db: SupportSQLiteDatabase) {
                db.execSQL("ALTER TABLE users ADD COLUMN email TEXT")
                db.execSQL("ALTER TABLE users ADD COLUMN username TEXT")
                db.execSQL("ALTER TABLE users ADD COLUMN department TEXT")
                db.execSQL("ALTER TABLE users ADD COLUMN isActive INTEGER NOT NULL DEFAULT 1")
                db.execSQL("ALTER TABLE users ADD COLUMN createdAt INTEGER NOT NULL DEFAULT 0")
                db.execSQL("ALTER TABLE users ADD COLUMN lastLoginAt INTEGER")
            }
        }

        // Migration from 24 to 25: Add admin role fields to users table
        val MIGRATION_24_25 = object : Migration(24, 25) {
            override fun migrate(db: SupportSQLiteDatabase) {
                db.execSQL("ALTER TABLE users ADD COLUMN role TEXT NOT NULL DEFAULT 'USER'")
                db.execSQL("ALTER TABLE users ADD COLUMN permissions TEXT")
            }
        }

        // Migration from 25 to 26: Add chat functionality
        val MIGRATION_25_26 = object : Migration(25, 26) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Create conversations table
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS conversations (
                        id TEXT NOT NULL PRIMARY KEY,
                        participant1Id TEXT NOT NULL,
                        participant2Id TEXT NOT NULL,
                        lastMessage TEXT,
                        lastMessageTime INTEGER NOT NULL,
                        lastMessageSenderId TEXT,
                        unreadCount1 INTEGER NOT NULL DEFAULT 0,
                        unreadCount2 INTEGER NOT NULL DEFAULT 0,
                        isArchived1 INTEGER NOT NULL DEFAULT 0,
                        isArchived2 INTEGER NOT NULL DEFAULT 0,
                        isMuted1 INTEGER NOT NULL DEFAULT 0,
                        isMuted2 INTEGER NOT NULL DEFAULT 0,
                        createdAt INTEGER NOT NULL,
                        FOREIGN KEY(participant1Id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY(participant2Id) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create chat_messages table
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS chat_messages (
                        id TEXT NOT NULL PRIMARY KEY,
                        conversationId TEXT NOT NULL,
                        senderId TEXT NOT NULL,
                        receiverId TEXT NOT NULL,
                        message TEXT NOT NULL,
                        messageType TEXT NOT NULL DEFAULT 'TEXT',
                        attachmentPath TEXT,
                        attachmentType TEXT,
                        timestamp INTEGER NOT NULL,
                        isRead INTEGER NOT NULL DEFAULT 0,
                        isDelivered INTEGER NOT NULL DEFAULT 1,
                        replyToMessageId TEXT,
                        isEdited INTEGER NOT NULL DEFAULT 0,
                        editedAt INTEGER,
                        FOREIGN KEY(senderId) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY(receiverId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create indexes for better performance
                db.execSQL("CREATE INDEX IF NOT EXISTS index_conversations_participant1Id ON conversations(participant1Id)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_conversations_participant2Id ON conversations(participant2Id)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_conversations_lastMessageTime ON conversations(lastMessageTime)")

                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_senderId ON chat_messages(senderId)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_receiverId ON chat_messages(receiverId)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_conversationId ON chat_messages(conversationId)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_timestamp ON chat_messages(timestamp)")
            }
        }

        // Migration from 30 to 31: Add phone field to users
        val MIGRATION_30_31 = object : Migration(30, 31) {
            override fun migrate(db: SupportSQLiteDatabase) {
                db.execSQL("ALTER TABLE users ADD COLUMN phone TEXT")
            }
        }

        // Migration from 31 to 32: Enhanced task system
        val MIGRATION_31_32 = object : Migration(31, 32) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Add new columns to tasks table
                db.execSQL("ALTER TABLE tasks ADD COLUMN description TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN creationDate INTEGER NOT NULL DEFAULT 0")
                db.execSQL("ALTER TABLE tasks ADD COLUMN completionDate INTEGER")
                db.execSQL("ALTER TABLE tasks ADD COLUMN priority TEXT NOT NULL DEFAULT 'MEDIUM'")
                db.execSQL("ALTER TABLE tasks ADD COLUMN category TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN tags TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN progress INTEGER NOT NULL DEFAULT 0")
                db.execSQL("ALTER TABLE tasks ADD COLUMN estimated_hours REAL")
                db.execSQL("ALTER TABLE tasks ADD COLUMN actual_hours REAL")
                db.execSQL("ALTER TABLE tasks ADD COLUMN assigned_user_id TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN parent_task_id INTEGER")
                db.execSQL("ALTER TABLE tasks ADD COLUMN attachments TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN notes TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN location TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN is_recurring INTEGER NOT NULL DEFAULT 0")
                db.execSQL("ALTER TABLE tasks ADD COLUMN recurring_pattern TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN color TEXT")
                db.execSQL("ALTER TABLE tasks ADD COLUMN is_archived INTEGER NOT NULL DEFAULT 0")
                db.execSQL("ALTER TABLE tasks ADD COLUMN energy_level TEXT NOT NULL DEFAULT 'MEDIUM'")
                db.execSQL("ALTER TABLE tasks ADD COLUMN focus_time_minutes INTEGER")
                db.execSQL("ALTER TABLE tasks ADD COLUMN importance TEXT NOT NULL DEFAULT 'MEDIUM'")

                // Create task_categories table
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS task_categories (
                        id TEXT NOT NULL PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        color TEXT NOT NULL,
                        icon TEXT,
                        is_default INTEGER NOT NULL DEFAULT 0,
                        created_by TEXT,
                        created_at INTEGER NOT NULL,
                        is_active INTEGER NOT NULL DEFAULT 1
                    )
                """)

                // Create subtasks table
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS subtasks (
                        id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
                        parent_task_id INTEGER NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT,
                        is_completed INTEGER NOT NULL DEFAULT 0,
                        order_index INTEGER NOT NULL DEFAULT 0,
                        assigned_user_id TEXT,
                        due_date INTEGER,
                        created_at INTEGER NOT NULL,
                        completed_at INTEGER,
                        estimated_minutes INTEGER,
                        actual_minutes INTEGER,
                        FOREIGN KEY(parent_task_id) REFERENCES tasks(id) ON DELETE CASCADE
                    )
                """)

                // Create indexes for better performance
                db.execSQL("CREATE INDEX IF NOT EXISTS index_tasks_priority ON tasks(priority)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_tasks_category ON tasks(category)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_tasks_assigned_user_id ON tasks(assigned_user_id)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_tasks_parent_task_id ON tasks(parent_task_id)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_tasks_is_completed ON tasks(isCompleted)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_tasks_is_archived ON tasks(is_archived)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_tasks_expirationDate ON tasks(expirationDate)")

                db.execSQL("CREATE INDEX IF NOT EXISTS index_subtasks_parent_task_id ON subtasks(parent_task_id)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_subtasks_is_completed ON subtasks(is_completed)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_subtasks_assigned_user_id ON subtasks(assigned_user_id)")
            }
        }

        // Migration from 26 to 30: Force recreate for chat functionality
        val MIGRATION_26_30 = object : Migration(26, 30) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Drop existing chat tables if they exist
                db.execSQL("DROP TABLE IF EXISTS conversations")
                db.execSQL("DROP TABLE IF EXISTS chat_messages")

                // Create conversations table
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS conversations (
                        id TEXT NOT NULL PRIMARY KEY,
                        participant1Id TEXT NOT NULL,
                        participant2Id TEXT NOT NULL,
                        lastMessage TEXT,
                        lastMessageTime INTEGER NOT NULL,
                        lastMessageSenderId TEXT,
                        unreadCount1 INTEGER NOT NULL DEFAULT 0,
                        unreadCount2 INTEGER NOT NULL DEFAULT 0,
                        isArchived1 INTEGER NOT NULL DEFAULT 0,
                        isArchived2 INTEGER NOT NULL DEFAULT 0,
                        isMuted1 INTEGER NOT NULL DEFAULT 0,
                        isMuted2 INTEGER NOT NULL DEFAULT 0,
                        createdAt INTEGER NOT NULL,
                        FOREIGN KEY(participant1Id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY(participant2Id) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create chat_messages table
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS chat_messages (
                        id TEXT NOT NULL PRIMARY KEY,
                        conversationId TEXT NOT NULL,
                        senderId TEXT NOT NULL,
                        receiverId TEXT NOT NULL,
                        message TEXT NOT NULL,
                        messageType TEXT NOT NULL DEFAULT 'TEXT',
                        attachmentPath TEXT,
                        attachmentType TEXT,
                        timestamp INTEGER NOT NULL,
                        isRead INTEGER NOT NULL DEFAULT 0,
                        isDelivered INTEGER NOT NULL DEFAULT 1,
                        replyToMessageId TEXT,
                        isEdited INTEGER NOT NULL DEFAULT 0,
                        editedAt INTEGER,
                        FOREIGN KEY(senderId) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY(receiverId) REFERENCES users(id) ON DELETE CASCADE
                    )
                """)

                // Create indexes for better performance
                db.execSQL("CREATE INDEX IF NOT EXISTS index_conversations_participant1Id ON conversations(participant1Id)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_conversations_participant2Id ON conversations(participant2Id)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_conversations_lastMessageTime ON conversations(lastMessageTime)")

                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_senderId ON chat_messages(senderId)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_receiverId ON chat_messages(receiverId)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_conversationId ON chat_messages(conversationId)")
                db.execSQL("CREATE INDEX IF NOT EXISTS index_chat_messages_timestamp ON chat_messages(timestamp)")
            }
        }

        // Define the migration from version 9 to 10 (Keep existing)
        val MIGRATION_9_10 = object : Migration(9, 10) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Add the new columns. Data migration from old 'owner' is complex
                // and might be better handled manually or ignored if fallback is used.
                // This ensures the schema matches the entity for version 10.
                db.execSQL("ALTER TABLE kpis ADD COLUMN owner_type TEXT")
                db.execSQL("ALTER TABLE kpis ADD COLUMN owner_name TEXT")
                // Note: The original 'owner' column might still exist if previous migrations failed.
                // The destructive fallback should handle cleanup if this migration isn't perfect.
            }
        }

        // Define the migration from version 10 to 11 (Keep existing)
        val MIGRATION_10_11 = object : Migration(10, 11) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Add the owner_image_path column to the kpis table
                db.execSQL("ALTER TABLE kpis ADD COLUMN owner_image_path TEXT")
            }
        }

        // Define the migration from version 11 to 12
        val MIGRATION_11_12 = object : Migration(11, 12) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Add the bottom_card_color_hex column to the kpis table
                db.execSQL("ALTER TABLE kpis ADD COLUMN bottom_card_color_hex TEXT")
            }
        }

        // Define the migration from version 12 to 13
        val MIGRATION_12_13 = object : Migration(12, 13) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Add the new color columns introduced after version 12
                db.execSQL("ALTER TABLE kpis ADD COLUMN card_color_hex TEXT")
                db.execSQL("ALTER TABLE kpis ADD COLUMN individual_card_color_hex TEXT")
            }
        }

        // Define the migration from version 13 to 14 to add master_kpi_id
        val MIGRATION_13_14 = object : Migration(13, 14) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Add the master_kpi_id column to the kpis table
                db.execSQL("ALTER TABLE kpis ADD COLUMN master_kpi_id TEXT")
            }
        }

        // Define the migration from version 20 to 21 to add user_id to kpi_progress_entries
        val MIGRATION_20_21 = object : Migration(20, 21) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Add the user_id column, making it non-nullable with a default value for existing rows
                db.execSQL("ALTER TABLE kpi_progress_entries ADD COLUMN user_id TEXT NOT NULL DEFAULT ''")
                // Add an index on the new user_id column
                db.execSQL("CREATE INDEX IF NOT EXISTS index_kpi_progress_entries_user_id ON kpi_progress_entries(user_id)")
                // Note: Adding Foreign Key constraints via ALTER TABLE is not directly supported in older SQLite versions.
                // Room validates the schema against the @Entity definition, including FKs.
            }
        }

        // Migration from 32 to 33: Add products table
        val MIGRATION_32_33 = object : Migration(32, 33) {
            override fun migrate(db: SupportSQLiteDatabase) {
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS `products` (
                        `id` TEXT NOT NULL,
                        `name` TEXT NOT NULL,
                        `category` TEXT NOT NULL,
                        `expiry_date` INTEGER NOT NULL,
                        `quantity` INTEGER NOT NULL,
                        `location` TEXT,
                        `notes` TEXT,
                        `created_at` INTEGER NOT NULL,
                        `updated_at` INTEGER NOT NULL,
                        `is_expired` INTEGER NOT NULL,
                        `days_until_expiry` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                """.trimIndent())
            }
        }

        // Migration from 33 to 34: Update old product categories
        val MIGRATION_33_34 = object : Migration(33, 34) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // Update old category names to new ones
                db.execSQL("UPDATE products SET category = 'MEDICINE' WHERE category = 'FOOD'")
                db.execSQL("UPDATE products SET category = 'BEAUTY' WHERE category = 'COSMETICS'")
                db.execSQL("UPDATE products SET category = 'MOM_BABY' WHERE category = 'BABY'")
                db.execSQL("UPDATE products SET category = 'MEDICINE' WHERE category = 'HEALTH'")
                db.execSQL("UPDATE products SET category = 'NUTRACEUTICAL' WHERE category = 'SUPPLEMENTS'")

                // Set any remaining unknown categories to MEDICINE
                db.execSQL("""
                    UPDATE products SET category = 'MEDICINE'
                    WHERE category NOT IN ('MEDICINE', 'MOM_BABY', 'PERSONAL_CARE', 'NUTRACEUTICAL', 'BEAUTY')
                """)
            }
        }

        // Singleton prevents multiple instances of database opening at the same time.
        @Volatile
        private var INSTANCE: AppDatabase? = null

        // Method to clear database for testing
        fun clearDatabase(context: Context) {
            INSTANCE?.close()
            INSTANCE = null
            context.deleteDatabase("kpi_database_v34_categories")
            // Also delete old databases if exist
            context.deleteDatabase("kpi_database_v33_products")
            context.deleteDatabase("kpi_database_v32_enhanced_tasks")
            context.deleteDatabase("kpi_database_v31_phone")
            context.deleteDatabase("kpi_database")
        }

        fun getDatabase(context: Context): AppDatabase {
            // if the INSTANCE is not null, then return it,
            // if it is, then create the database
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "kpi_database_v34_categories"
                )
                .addMigrations(MIGRATION_32_33, MIGRATION_33_34)
                .fallbackToDestructiveMigration() // This will delete all data and recreate tables
                .fallbackToDestructiveMigrationOnDowngrade() // Handle downgrades too
                .build()
                INSTANCE = instance
                // return instance
                instance
            }
        }
    }
}
