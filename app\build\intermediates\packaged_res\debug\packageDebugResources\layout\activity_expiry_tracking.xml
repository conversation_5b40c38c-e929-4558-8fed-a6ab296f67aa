<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.ExpiryTrackingActivity">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="إدارة انتهاء الصلاحية"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:gravity="center"
                android:layout_marginBottom="24dp" />

            <!-- Current Date Display -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/datePickerCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/primary_color"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                android:background="?android:attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_calendar"
                        app:tint="@color/primary_color"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:id="@+id/currentDateText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="التاريخ المحدد: 15-07-2025"
                        android:textSize="18sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_baseline_keyboard_arrow_down_24"
                        app:tint="@color/text_secondary"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Statistics Cards -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp">

                <!-- Expired Products Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    app:cardBackgroundColor="@color/error_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/ic_warning"
                            app:tint="@color/error_color"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/expiredCountText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 منتج منتهي الصلاحية"
                            android:textSize="14sp"
                            android:textColor="@color/error_color"
                            android:textStyle="bold"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Expiring Soon Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    app:cardBackgroundColor="@color/warning_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/ic_time"
                            app:tint="@color/warning_color"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/expiringSoonCountText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 منتج ينتهي قريباً"
                            android:textSize="14sp"
                            android:textColor="@color/warning_color"
                            android:textStyle="bold"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Action Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/expiryActionCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="0dp"
                app:cardBackgroundColor="@color/error_light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_warning"
                        app:tint="@color/error_color"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إجراء مطلوب"
                            android:textSize="16sp"
                            android:textColor="@color/error_color"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/actionMessageText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="المطلوب: إرجاع منتهيات شهر يوليو"
                            android:textSize="14sp"
                            android:textColor="@color/error_color"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_forward"
                        app:tint="@color/error_color" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- View Expired Products Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/viewExpiredProductsButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="عرض المنتجات المنتهية الصلاحية"
                    android:textSize="16sp"
                    android:padding="16dp"
                    app:icon="@drawable/ic_warning"
                    app:iconGravity="textStart"
                    app:backgroundTint="@color/error_color"
                    app:cornerRadius="12dp" />

                <!-- View Expiring Soon Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/viewExpiringSoonButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="عرض المنتجات التي تنتهي قريباً"
                    android:textSize="16sp"
                    android:padding="16dp"
                    app:icon="@drawable/ic_time"
                    app:iconGravity="textStart"
                    app:backgroundTint="@color/warning_color"
                    app:cornerRadius="12dp"
                    style="@style/Widget.MaterialComponents.Button" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
