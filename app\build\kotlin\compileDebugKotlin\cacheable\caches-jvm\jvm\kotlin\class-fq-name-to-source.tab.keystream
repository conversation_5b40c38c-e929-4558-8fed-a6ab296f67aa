0com.example.kpitrackerapp.AdminDashboardActivity=com.example.kpitrackerapp.AdminDashboardActivity.ActivityItem=com.example.kpitrackerapp.AdminDashboardActivity.TopPerformer/com.example.kpitrackerapp.KpiTrackerApplication9com.example.kpitrackerapp.KpiTrackerApplication.Companion&com.example.kpitrackerapp.MainActivity8com.example.kpitrackerapp.adapters.AdminDashboardAdapterBcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.CompanionIcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.HeaderViewHolderKcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.StatCardViewHolderMcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActionCardViewHolderOcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.ActivityCardViewHolderKcom.example.kpitrackerapp.adapters.AdminDashboardAdapter.UserCardViewHolder5com.example.kpitrackerapp.adapters.ChatMessageAdapter?com.example.kpitrackerapp.adapters.ChatMessageAdapter.CompanionJcom.example.kpitrackerapp.adapters.ChatMessageAdapter.DateHeaderViewHolderKcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SentMessageViewHolderOcom.example.kpitrackerapp.adapters.ChatMessageAdapter.ReceivedMessageViewHolderMcom.example.kpitrackerapp.adapters.ChatMessageAdapter.SystemMessageViewHolder:com.example.kpitrackerapp.adapters.ChatMessageDiffCallback6com.example.kpitrackerapp.adapters.ConversationAdapterMcom.example.kpitrackerapp.adapters.ConversationAdapter.ConversationViewHolder;com.example.kpitrackerapp.adapters.ConversationDiffCallback6com.example.kpitrackerapp.adapters.NotificationAdapterMcom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationViewHolderOcom.example.kpitrackerapp.adapters.NotificationAdapter.NotificationDiffCallback4com.example.kpitrackerapp.adapters.UserFilterAdapterCcom.example.kpitrackerapp.adapters.UserFilterAdapter.UserViewHolder2com.example.kpitrackerapp.adapters.UserListAdapterAcom.example.kpitrackerapp.adapters.UserListAdapter.UserViewHolder3com.example.kpitrackerapp.adapters.UserDiffCallback3com.example.kpitrackerapp.fragments.AccountFragment5com.example.kpitrackerapp.fragments.DashboardFragment9com.example.kpitrackerapp.fragments.MainDashboardFragment4com.example.kpitrackerapp.fragments.MessagesFragment7com.example.kpitrackerapp.fragments.PerformanceFragment/com.example.kpitrackerapp.models.DashboardStats0com.example.kpitrackerapp.models.UserPerformance,com.example.kpitrackerapp.models.ActivityLog-com.example.kpitrackerapp.models.ActivityType-com.example.kpitrackerapp.models.SystemHealth3com.example.kpitrackerapp.models.UserManagementItem2com.example.kpitrackerapp.models.KpiManagementItem1com.example.kpitrackerapp.models.AdminPermissions;com.example.kpitrackerapp.models.AdminPermissions.Companion3com.example.kpitrackerapp.models.AdminDashboardItem:com.example.kpitrackerapp.models.AdminDashboardItem.Header<com.example.kpitrackerapp.models.AdminDashboardItem.StatCard><EMAIL><com.example.kpitrackerapp.models.AdminDashboardItem.UserCard,com.example.kpitrackerapp.models.ChatMessage-com.example.kpitrackerapp.models.Conversation,com.example.kpitrackerapp.models.MessageType/com.example.kpitrackerapp.models.AttachmentType0com.example.kpitrackerapp.models.ChatMessageItem1com.example.kpitrackerapp.models.ConversationItem)com.example.kpitrackerapp.models.ChatUser/com.example.kpitrackerapp.models.SystemMessages*com.example.kpitrackerapp.models.ChatEvent5com.example.kpitrackerapp.models.ChatEvent.NewMessage6com.example.kpitrackerapp.models.ChatEvent.MessageRead;com.example.kpitrackerapp.models.ChatEvent.MessageDelivered5com.example.kpitrackerapp.models.ChatEvent.UserTyping<com.example.kpitrackerapp.models.ChatEvent.UserStoppedTyping5com.example.kpitrackerapp.models.ChatEvent.UserOnline6com.example.kpitrackerapp.models.ChatEvent.UserOffline(com.example.kpitrackerapp.models.KpiUnit$com.example.kpitrackerapp.models.Kpi1com.example.kpitrackerapp.models.KpiProgressEntry.com.example.kpitrackerapp.models.OcrResultItem.com.example.kpitrackerapp.models.SmartListItem(com.example.kpitrackerapp.models.Subtask4com.example.kpitrackerapp.models.SubtaskWithProgress%com.example.kpitrackerapp.models.Task-com.example.kpitrackerapp.models.TaskPriority/com.example.kpitrackerapp.models.TaskImportance,com.example.kpitrackerapp.models.EnergyLevel-com.example.kpitrackerapp.models.TaskQuadrant.com.example.kpitrackerapp.models.TaskAnalytics.com.example.kpitrackerapp.models.DailyProgress-com.example.kpitrackerapp.models.MonthlyTrend5com.example.kpitrackerapp.models.EisenhowerMatrixData5com.example.kpitrackerapp.models.ProductivityInsights4com.example.kpitrackerapp.models.WorkPatternAnalysis-com.example.kpitrackerapp.models.GoalAnalysis1com.example.kpitrackerapp.models.BehaviorInsights+com.example.kpitrackerapp.models.QuickStats.com.example.kpitrackerapp.models.TimeAnalytics-com.example.kpitrackerapp.models.TaskCategory6com.example.kpitrackerapp.models.DefaultTaskCategories%com.example.kpitrackerapp.models.User)com.example.kpitrackerapp.models.UserRole/com.example.kpitrackerapp.models.UserFilterItem2com.example.kpitrackerapp.models.UserKpiAssignment1com.example.kpitrackerapp.persistence.AppDatabase;com.example.kpitrackerapp.persistence.AppDatabase.Companion-com.example.kpitrackerapp.persistence.ChatDao=com.example.kpitrackerapp.persistence.ConversationWithDetails0com.example.kpitrackerapp.persistence.Converters,com.example.kpitrackerapp.persistence.KpiDao9com.example.kpitrackerapp.persistence.KpiProgressEntryDao0com.example.kpitrackerapp.persistence.SubtaskDao5com.example.kpitrackerapp.persistence.TaskCategoryDao3com.example.kpitrackerapp.persistence.CategoryCount3com.example.kpitrackerapp.persistence.PriorityCount-com.example.kpitrackerapp.persistence.TaskDao-com.example.kpitrackerapp.persistence.UserDao:com.example.kpitrackerapp.persistence.UserKpiAssignmentDao5com.example.kpitrackerapp.repositories.ChatRepository>com.example.kpitrackerapp.services.KPIFirebaseMessagingService8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivityBcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.CompanionCcom.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity.SpeechMode/com.example.kpitrackerapp.ui.AddEditKpiActivity9com.example.kpitrackerapp.ui.AddEditKpiActivity.Companion7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivityAcom.example.kpitrackerapp.ui.AddEditKpiOriginalActivity.Companion:com.example.kpitrackerapp.ui.AddEditProgressDialogFragmentDcom.example.kpitrackerapp.ui.AddEditProgressDialogFragment.Companion5com.example.kpitrackerapp.ui.AutoSendSettingsActivity,com.example.kpitrackerapp.ui.ChartMarkerView)com.example.kpitrackerapp.ui.ChatActivity-com.example.kpitrackerapp.ui.ChatListActivity1com.example.kpitrackerapp.ui.ColoredReportAdapterBcom.example.kpitrackerapp.ui.ColoredReportAdapter.ReportViewHolderDcom.example.kpitrackerapp.ui.ColoredReportAdapter.ReportDiffCallback1com.example.kpitrackerapp.ui.CompactReportAdapterIcom.example.kpitrackerapp.ui.CompactReportAdapter.CompactReportViewHolderDcom.example.kpitrackerapp.ui.CompactReportAdapter.ReportDiffCallback/com.example.kpitrackerapp.ui.CreateUserActivity0com.example.kpitrackerapp.ui.EnhancedTaskAdapter?com.example.kpitrackerapp.ui.EnhancedTaskAdapter.TaskViewHolderAcom.example.kpitrackerapp.ui.EnhancedTaskAdapter.TaskDiffCallback8com.example.kpitrackerapp.ui.EnhancedTaskActionsListener0com.example.kpitrackerapp.ui.ExcelImportActivity:com.example.kpitrackerapp.ui.ExcelImportActivity.Companion0com.example.kpitrackerapp.ui.ExcelReviewActivity:com.example.kpitrackerapp.ui.ExcelReviewActivity.Companion/com.example.kpitrackerapp.ui.ExcelReviewAdapterEcom.example.kpitrackerapp.ui.ExcelReviewAdapter.ExcelReviewViewHolderAcom.example.kpitrackerapp.ui.ExcelReviewAdapter.ExcelDiffCallback5com.example.kpitrackerapp.ui.ExpireManagementActivity.com.example.kpitrackerapp.ui.KpiDetailActivity8com.example.kpitrackerapp.ui.KpiDetailActivity.CompanionEcom.example.kpitrackerapp.ui.KpiDetailActivity.DateAxisValueFormatter1com.example.kpitrackerapp.ui.OnKpiActionsListener+com.example.kpitrackerapp.ui.KpiListAdapter9com.example.kpitrackerapp.ui.KpiListAdapter.KpiViewHolderGcom.example.kpitrackerapp.ui.KpiListAdapter.KpiWithProgressDiffCallback4com.example.kpitrackerapp.ui.KpiProgressEntryAdapterLcom.example.kpitrackerapp.ui.KpiProgressEntryAdapter.ProgressEntryViewHolderNcom.example.kpitrackerapp.ui.KpiProgressEntryAdapter.ProgressEntryDiffCallback*com.example.kpitrackerapp.ui.LoginActivity1com.example.kpitrackerapp.ui.ModernReportActivity=com.example.kpitrackerapp.ui.ModernReportActivity.ChartPeriod;com.example.kpitrackerapp.ui.ModernReportActivity.CompanionCcom.example.kpitrackerapp.ui.ModernReportActivity.BarChartEntryData2com.example.kpitrackerapp.ui.NotificationsActivity(com.example.kpitrackerapp.ui.OcrActivity2com.example.kpitrackerapp.ui.OcrActivity.Companion.com.example.kpitrackerapp.ui.OcrReviewActivity8com.example.kpitrackerapp.ui.OcrReviewActivity.Companion-com.example.kpitrackerapp.ui.OcrReviewAdapter8com.example.kpitrackerapp.ui.OcrReviewAdapter.ViewHolder/com.example.kpitrackerapp.ui.RecentUsersAdapterDcom.example.kpitrackerapp.ui.RecentUsersAdapter.RecentUserViewHolderFcom.example.kpitrackerapp.ui.RecentUsersAdapter.RecentUserDiffCallback+com.example.kpitrackerapp.ui.ReportActivity7com.example.kpitrackerapp.ui.ReportActivity.ChartPeriod5com.example.kpitrackerapp.ui.ReportActivity.Companion*com.example.kpitrackerapp.ui.KpiReportData*com.example.kpitrackerapp.ui.ReportAdapter;com.example.kpitrackerapp.ui.ReportAdapter.ReportViewHolder=com.example.kpitrackerapp.ui.ReportAdapter.ReportDiffCallback7com.example.kpitrackerapp.ui.SearchEditProgressActivityAcom.example.kpitrackerapp.ui.SearchEditProgressActivity.Companion2com.example.kpitrackerapp.ui.OnTaskActionsListener(com.example.kpitrackerapp.ui.TaskAdapter7com.example.kpitrackerapp.ui.TaskAdapter.TaskViewHolder-com.example.kpitrackerapp.ui.TaskDiffCallback3com.example.kpitrackerapp.ui.TaskManagementActivity9com.example.kpitrackerapp.ui.TaskReminderSettingsActivity/com.example.kpitrackerapp.ui.TaskReportActivity.com.example.kpitrackerapp.ui.TaskReportAdapterCcom.example.kpitrackerapp.ui.TaskReportAdapter.TaskReportViewHolder-com.example.kpitrackerapp.ui.UnifiedReportRow1com.example.kpitrackerapp.ui.UnifiedReportAdapterEcom.example.kpitrackerapp.ui.UnifiedReportAdapter.ReportRowViewHolderGcom.example.kpitrackerapp.ui.UnifiedReportAdapter.ReportRowDiffCallback-com.example.kpitrackerapp.ui.UserFilterDialog0com.example.kpitrackerapp.ui.UserKpiListActivity:com.example.kpitrackerapp.ui.UserKpiListActivity.Companion9com.example.kpitrackerapp.ui.OnUserSummaryActionsListener/com.example.kpitrackerapp.ui.UserSummaryAdapterEcom.example.kpitrackerapp.ui.UserSummaryAdapter.UserSummaryViewHolderGcom.example.kpitrackerapp.ui.UserSummaryAdapter.UserSummaryDiffCallback-com.example.kpitrackerapp.ui.KpiSummaryDetail,<EMAIL>=com.example.kpitrackerapp.utils.CardAnimationHelper.CompanionBcom.example.kpitrackerapp.utils.CardAnimationHelper.SwipeDirection1com.example.kpitrackerapp.utils.CardGestureHelper7com.example.kpitrackerapp.utils.CardInteractionListener.com.example.kpitrackerapp.utils.DragDropHelper6com.example.kpitrackerapp.utils.EisenhowerMatrixHelper2com.example.kpitrackerapp.utils.EisenhowerQuadrant5com.example.kpitrackerapp.utils.EisenhowerMatrixStats6com.example.kpitrackerapp.utils.FirebaseMessageManager/com.example.kpitrackerapp.utils.LanguageManager6com.example.kpitrackerapp.utils.AppNotificationManagerGcom.example.kpitrackerapp.utils.AppNotificationManager.NotificationItem4com.example.kpitrackerapp.utils.RecurringTaskManager>com.example.kpitrackerapp.utils.RecurringTaskManager.Companion3com.example.kpitrackerapp.utils.RecurringTasksStats.com.example.kpitrackerapp.utils.SessionManager,com.example.kpitrackerapp.utils.ThemeManager2com.example.kpitrackerapp.viewmodels.ChatViewModel9com.example.kpitrackerapp.viewmodels.ChatViewModelFactory3com.example.kpitrackerapp.viewmodels.MasterCardData)com.example.kpitrackerapp.viewmodels.Quad1com.example.kpitrackerapp.viewmodels.KpiViewModel8com.example.kpitrackerapp.viewmodels.KpiViewModelFactory4com.example.kpitrackerapp.viewmodels.KpiWithProgress2com.example.kpitrackerapp.viewmodels.TaskViewModel9com.example.kpitrackerapp.viewmodels.TaskViewModelFactory8com.example.kpitrackerapp.workers.AutoReportSenderWorkerBcom.example.kpitrackerapp.workers.AutoReportSenderWorker.Companion:com.example.kpitrackerapp.workers.ExpiryNotificationWorkerDcom.example.kpitrackerapp.workers.ExpiryNotificationWorker.Companion5com.example.kpitrackerapp.workers.RecurringTaskWorker?com.example.kpitrackerapp.workers.RecurringTaskWorker.Companion4com.example.kpitrackerapp.workers.TaskReminderWorker>com.example.kpitrackerapp.workers.TaskReminderWorker.CompanionCcom.example.kpitrackerapp.databinding.DialogSelectCardColorsBinding><EMAIL>=<EMAIL>=com.example.kpitrackerapp.databinding.UserFilterDialogBinding>com.example.kpitrackerapp.databinding.FragmentDashboardBinding=com.example.kpitrackerapp.databinding.FragmentMessagesBindingBcom.example.kpitrackerapp.databinding.CompactReportTableRowBindingBcom.example.kpitrackerapp.databinding.ReportTableRowColoredBinding:com.example.kpitrackerapp.databinding.OcrReviewItemBinding;com.example.kpitrackerapp.databinding.ActivityReportBinding?com.example.kpitrackerapp.databinding.ActivityTaskReportBindingEcom.example.kpitrackerapp.databinding.ActivityExpireManagementBindingCcom.example.kpitrackerapp.databinding.OverallSummaryCardItemBindingAcom.example.kpitrackerapp.databinding.KpiSummaryDetailItemBinding>com.example.kpitrackerapp.databinding.ActivityOcrReviewBinding?com.example.kpitrackerapp.databinding.ActivityCreateUserBinding?com.example.kpitrackerapp.databinding.ActivityAddEditKpiBinding:com.example.kpitrackerapp.databinding.ActivityLoginBindingAcom.example.kpitrackerapp.databinding.ActivityModernReportBindingBcom.example.kpitrackerapp.databinding.FragmentMainDashboardBindingCcom.example.kpitrackerapp.databinding.ActivityAdminDashboardBindingIcom.example.kpitrackerapp.databinding.ActivityTaskReminderSettingsBinding%com.example.kpitrackerapp.BuildConfigGcom.example.kpitrackerapp.databinding.ActivityAddEditKpiOriginalBindingGcom.example.kpitrackerapp.databinding.ActivitySearchEditProgressBindingIcom.example.kpitrackerapp.databinding.UnifiedReportTableRowBindingBinding><EMAIL><com.example.kpitrackerapp.databinding.ExcelReviewItemBinding=com.example.kpitrackerapp.databinding.ItemTaskEnhancedBinding;com.example.kpitrackerapp.databinding.ItemRecentUserBinding<<EMAIL>@com.example.kpitrackerapp.databinding.ActivityUserKpiListBinding1com.example.kpitrackerapp.utils.AlarmClockManager;com.example.kpitrackerapp.utils.AlarmClockManager.Companion2com.example.kpitrackerapp.ui.ModernAddTaskActivityBcom.example.kpitrackerapp.databinding.ActivityAddTaskModernBinding7com.example.kpitrackerapp.adapters.SearchResultsAdapterNcom.example.kpitrackerapp.adapters.SearchResultsAdapter.SearchResultViewHolderPcom.example.kpitrackerapp.adapters.SearchResultsAdapter.SearchResultDiffCallback2com.example.kpitrackerapp.ui.PomodoroTimerActivityAcom.example.kpitrackerapp.databinding.DialogSearchMessagesBindingAcom.example.kpitrackerapp.databinding.DialogCustomReminderBindingAcom.example.kpitrackerapp.databinding.DialogCustomPomodoroBinding=com.example.kpitrackerapp.databinding.ItemSearchResultBindingBcom.example.kpitrackerapp.databinding.ActivityPomodoroTimerBinding2com.example.kpitrackerapp.persistence.ChatDao_Impl2com.example.kpitrackerapp.ui.DateConverterActivity-com.example.kpitrackerapp.utils.HijriCalendar7com.example.kpitrackerapp.utils.HijriCalendar.HijriDateBcom.example.kpitrackerapp.databinding.ActivityDateConverterBinding5com.example.kpitrackerapp.fragments.DrugIndexFragment>com.example.kpitrackerapp.databinding.FragmentDrugIndexBindingFcom.example.kpitrackerapp.fragments.DrugIndexFragment.DrugSearchResultDcom.example.kpitrackerapp.fragments.DrugIndexFragment.DrugDetailInfoEcom.example.kpitrackerapp.fragments.DrugIndexFragment.SDISearchResultHcom.example.kpitrackerapp.fragments.DrugIndexFragment.GoogleSearchResult>com.example.kpitrackerapp.fragments.DrugIndexFragment.DrugInfo(com.example.kpitrackerapp.models.Product0com.example.kpitrackerapp.models.ProductCategory0com.example.kpitrackerapp.persistence.ProductDao8com.example.kpitrackerapp.repositories.ProductRepository/com.example.kpitrackerapp.ui.AddProductActivity5com.example.kpitrackerapp.viewmodels.ProductViewModel3com.example.kpitrackerapp.viewmodels.ProductUiState<com.example.kpitrackerapp.viewmodels.ProductViewModelFactory?com.example.kpitrackerapp.databinding.ActivityAddProductBinding6com.example.kpitrackerapp.persistence.AppDatabase_Impl5com.example.kpitrackerapp.persistence.ProductDao_Impl                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    