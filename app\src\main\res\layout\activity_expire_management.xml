<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.ExpireManagementActivity">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Summary Cards Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp">

                <!-- Total Products Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/totalProductsCard"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:strokeColor="@color/blue_500"
                    app:strokeWidth="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_dashboard_24"
                            android:tint="@color/blue_500"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/totalProductsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/blue_500" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إجمالي المنتجات"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Expired Products Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/expiredProductsCard"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:strokeColor="@color/red_500"
                    app:strokeWidth="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_notification"
                            android:tint="@color/red_500"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/expiredProductsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/red_500" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="منتجات منتهية"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Expiring Soon Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/expiringProductsCard"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:strokeColor="@color/orange_500"
                    app:strokeWidth="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_time"
                            android:tint="@color/orange_500"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/expiringProductsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/orange_500" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تنتهي قريباً"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Categories Section Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="المنتجات حسب الفئة"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="16dp" />

            <!-- Categories Grid -->
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:rowCount="3">

                <!-- Medicine Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/medicineCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_health">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💊"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/medicineCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Medicine"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Mom&baby Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/momBabyCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_family">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="👶"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/momBabyCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mom&baby"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Personal Care Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/personalCareCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_work">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🧴"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/personalCareCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Personal care"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Nutraceutical Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/nutraceuticalCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_personal">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🌿"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/nutraceuticalCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Nutraceutical"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Beauty Category -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/beautyCard"
                    android:layout_width="0dp"
                    android:layout_height="100dp"
                    android:layout_columnWeight="1"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/category_education">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💄"
                            android:textSize="32sp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/beautyCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Beauty"
                            android:textSize="12sp"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </GridLayout>

            <!-- Expiry Management Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="32dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="إدارة انتهاء الصلاحية"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- Current Date Display -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_calendar"
                            app:tint="@color/primary_color"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:id="@+id/currentDateText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="التاريخ المحدد: 15-07-2025"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Action Button -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/expiryActionCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp"
                    app:cardBackgroundColor="@color/error_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_warning"
                            app:tint="@color/error_color"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="المطلوب: إرجاع منتهيات شهر August"
                            android:textSize="16sp"
                            android:textColor="@color/error_color"
                            android:textStyle="bold" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_arrow_forward"
                            app:tint="@color/error_color" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Expiry Tracking Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/openExpiryTrackingButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="فتح إدارة انتهاء الصلاحية التفصيلية"
                    android:textSize="16sp"
                    android:padding="16dp"
                    app:icon="@drawable/ic_calendar"
                    app:iconGravity="textStart"
                    app:backgroundTint="@color/primary_color"
                    app:cornerRadius="12dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addProductFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_check"
        app:tint="@color/white"
        app:backgroundTint="@color/primary_color" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
