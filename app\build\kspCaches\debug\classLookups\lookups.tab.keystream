  Activity android.app  Application android.app  Dialog android.app  Context android.content  ContextWrapper android.content  ContextThemeWrapper android.view  View android.view  	ViewGroup android.view  RelativeLayout android.widget  ComponentActivity androidx.activity  AppCompatActivity androidx.appcompat.app  ComponentActivity androidx.core.app  DialogFragment androidx.fragment.app  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  ItemTouchHelper androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Callback ,androidx.recyclerview.widget.ItemTouchHelper  SimpleCallback ,androidx.recyclerview.widget.ItemTouchHelper  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  
ForeignKey 
androidx.room  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion androidx.room.ForeignKey  	Companion  androidx.room.OnConflictStrategy  	Companion androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  
Configuration 
androidx.work  CoroutineWorker 
androidx.work  ListenableWorker 
androidx.work  AdminDashboardActivity com.example.kpitrackerapp  KpiTrackerApplication com.example.kpitrackerapp  MainActivity com.example.kpitrackerapp  	Companion /com.example.kpitrackerapp.KpiTrackerApplication  AdminDashboardAdapter "com.example.kpitrackerapp.adapters  ChatMessageAdapter "com.example.kpitrackerapp.adapters  ConversationAdapter "com.example.kpitrackerapp.adapters  NotificationAdapter "com.example.kpitrackerapp.adapters  ProductListAdapter "com.example.kpitrackerapp.adapters  UserFilterAdapter "com.example.kpitrackerapp.adapters  UserListAdapter "com.example.kpitrackerapp.adapters  ActionCardViewHolder 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  ActivityCardViewHolder 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  	Companion 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  HeaderViewHolder 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  StatCardViewHolder 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  UserCardViewHolder 8com.example.kpitrackerapp.adapters.AdminDashboardAdapter  	Companion 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  DateHeaderViewHolder 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  ReceivedMessageViewHolder 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  SentMessageViewHolder 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  SystemMessageViewHolder 5com.example.kpitrackerapp.adapters.ChatMessageAdapter  ConversationViewHolder 6com.example.kpitrackerapp.adapters.ConversationAdapter  NotificationViewHolder 6com.example.kpitrackerapp.adapters.NotificationAdapter  UserViewHolder 4com.example.kpitrackerapp.adapters.UserFilterAdapter  UserViewHolder 2com.example.kpitrackerapp.adapters.UserListAdapter  AccountFragment #com.example.kpitrackerapp.fragments  DashboardFragment #com.example.kpitrackerapp.fragments  DrugIndexFragment #com.example.kpitrackerapp.fragments  MainDashboardFragment #com.example.kpitrackerapp.fragments  MessagesFragment #com.example.kpitrackerapp.fragments  PerformanceFragment #com.example.kpitrackerapp.fragments  ChatMessage  com.example.kpitrackerapp.models  Conversation  com.example.kpitrackerapp.models  Kpi  com.example.kpitrackerapp.models  KpiProgressEntry  com.example.kpitrackerapp.models  
OcrResultItem  com.example.kpitrackerapp.models  Product  com.example.kpitrackerapp.models  
SmartListItem  com.example.kpitrackerapp.models  Subtask  com.example.kpitrackerapp.models  Task  com.example.kpitrackerapp.models  TaskCategory  com.example.kpitrackerapp.models  User  com.example.kpitrackerapp.models  UserFilterItem  com.example.kpitrackerapp.models  UserKpiAssignment  com.example.kpitrackerapp.models  AppDatabase %com.example.kpitrackerapp.persistence  
CategoryCount %com.example.kpitrackerapp.persistence  ChatDao %com.example.kpitrackerapp.persistence  ConversationWithDetails %com.example.kpitrackerapp.persistence  
Converters %com.example.kpitrackerapp.persistence  KpiDao %com.example.kpitrackerapp.persistence  KpiProgressEntryDao %com.example.kpitrackerapp.persistence  
PriorityCount %com.example.kpitrackerapp.persistence  
ProductDao %com.example.kpitrackerapp.persistence  
SubtaskDao %com.example.kpitrackerapp.persistence  TaskCategoryDao %com.example.kpitrackerapp.persistence  TaskDao %com.example.kpitrackerapp.persistence  UserDao %com.example.kpitrackerapp.persistence  UserKpiAssignmentDao %com.example.kpitrackerapp.persistence  	Companion 1com.example.kpitrackerapp.persistence.AppDatabase  <no name provided> Kcom.example.kpitrackerapp.persistence.AppDatabase.Companion.MIGRATION_32_33  AddEditAdvancedTaskActivity com.example.kpitrackerapp.ui  AddEditKpiActivity com.example.kpitrackerapp.ui  AddEditKpiOriginalActivity com.example.kpitrackerapp.ui  AddEditProgressDialogFragment com.example.kpitrackerapp.ui  AddProductActivity com.example.kpitrackerapp.ui  AutoSendSettingsActivity com.example.kpitrackerapp.ui  ChartMarkerView com.example.kpitrackerapp.ui  ChatActivity com.example.kpitrackerapp.ui  ChatListActivity com.example.kpitrackerapp.ui  ColoredReportAdapter com.example.kpitrackerapp.ui  CompactReportAdapter com.example.kpitrackerapp.ui  CreateUserActivity com.example.kpitrackerapp.ui  DateConverterActivity com.example.kpitrackerapp.ui  ExcelImportActivity com.example.kpitrackerapp.ui  ExcelReviewActivity com.example.kpitrackerapp.ui  ExcelReviewAdapter com.example.kpitrackerapp.ui  ExpireManagementActivity com.example.kpitrackerapp.ui  ExpiryTrackingActivity com.example.kpitrackerapp.ui  KpiDetailActivity com.example.kpitrackerapp.ui  KpiProgressEntryAdapter com.example.kpitrackerapp.ui  
LoginActivity com.example.kpitrackerapp.ui  ModernAddTaskActivity com.example.kpitrackerapp.ui  ModernReportActivity com.example.kpitrackerapp.ui  NotificationsActivity com.example.kpitrackerapp.ui  OcrActivity com.example.kpitrackerapp.ui  OcrReviewActivity com.example.kpitrackerapp.ui  OcrReviewAdapter com.example.kpitrackerapp.ui  PomodoroTimerActivity com.example.kpitrackerapp.ui  ProductListActivity com.example.kpitrackerapp.ui  ReportActivity com.example.kpitrackerapp.ui  SearchEditProgressActivity com.example.kpitrackerapp.ui  TaskAdapter com.example.kpitrackerapp.ui  TaskManagementActivity com.example.kpitrackerapp.ui  TaskReminderSettingsActivity com.example.kpitrackerapp.ui  TaskReportActivity com.example.kpitrackerapp.ui  TaskReportAdapter com.example.kpitrackerapp.ui  UnifiedReportAdapter com.example.kpitrackerapp.ui  UserFilterDialog com.example.kpitrackerapp.ui  UserKpiListActivity com.example.kpitrackerapp.ui  UserSummaryAdapter com.example.kpitrackerapp.ui  	Companion 8com.example.kpitrackerapp.ui.AddEditAdvancedTaskActivity  	Companion /com.example.kpitrackerapp.ui.AddEditKpiActivity  	Companion 7com.example.kpitrackerapp.ui.AddEditKpiOriginalActivity  	Companion :com.example.kpitrackerapp.ui.AddEditProgressDialogFragment  	Companion 0com.example.kpitrackerapp.ui.ExcelImportActivity  	Companion 0com.example.kpitrackerapp.ui.ExcelReviewActivity  	Companion .com.example.kpitrackerapp.ui.KpiDetailActivity  	Companion 1com.example.kpitrackerapp.ui.ModernReportActivity  	Companion (com.example.kpitrackerapp.ui.OcrActivity  	Companion .com.example.kpitrackerapp.ui.OcrReviewActivity  
ViewHolder -com.example.kpitrackerapp.ui.OcrReviewAdapter  	Companion 0com.example.kpitrackerapp.ui.ProductListActivity  	Companion +com.example.kpitrackerapp.ui.ReportActivity  	Companion 7com.example.kpitrackerapp.ui.SearchEditProgressActivity  TaskViewHolder (com.example.kpitrackerapp.ui.TaskAdapter  TaskReportViewHolder .com.example.kpitrackerapp.ui.TaskReportAdapter  	Companion 0com.example.kpitrackerapp.ui.UserKpiListActivity  AppNotificationManager com.example.kpitrackerapp.utils  CardGestureHelper com.example.kpitrackerapp.utils  DragDropHelper com.example.kpitrackerapp.utils  SessionManager com.example.kpitrackerapp.utils  
ChatViewModel $com.example.kpitrackerapp.viewmodels  KpiViewModel $com.example.kpitrackerapp.viewmodels  KpiWithProgress $com.example.kpitrackerapp.viewmodels  ProductViewModel $com.example.kpitrackerapp.viewmodels  
TaskViewModel $com.example.kpitrackerapp.viewmodels  AutoReportSenderWorker !com.example.kpitrackerapp.workers  ExpiryNotificationWorker !com.example.kpitrackerapp.workers  TaskReminderWorker !com.example.kpitrackerapp.workers  	Companion 8com.example.kpitrackerapp.workers.AutoReportSenderWorker  	Companion :com.example.kpitrackerapp.workers.ExpiryNotificationWorker  	Companion 4com.example.kpitrackerapp.workers.TaskReminderWorker  
MarkerView 'com.github.mikephil.charting.components  String kotlin  Service android.app  ContextMenu android.view  ViewModelProvider androidx.lifecycle  DiffUtil androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  ChatMessageDiffCallback "com.example.kpitrackerapp.adapters  ConversationDiffCallback "com.example.kpitrackerapp.adapters  SearchResultsAdapter "com.example.kpitrackerapp.adapters  UserDiffCallback "com.example.kpitrackerapp.adapters  NotificationDiffCallback 6com.example.kpitrackerapp.adapters.NotificationAdapter  KPIFirebaseMessagingService "com.example.kpitrackerapp.services  EnhancedTaskActionsListener com.example.kpitrackerapp.ui  EnhancedTaskAdapter com.example.kpitrackerapp.ui  KpiListAdapter com.example.kpitrackerapp.ui  OnKpiActionsListener com.example.kpitrackerapp.ui  OnUserSummaryActionsListener com.example.kpitrackerapp.ui  RecentUsersAdapter com.example.kpitrackerapp.ui  
ReportAdapter com.example.kpitrackerapp.ui  TaskDiffCallback com.example.kpitrackerapp.ui  ReportDiffCallback 1com.example.kpitrackerapp.ui.ColoredReportAdapter  ReportDiffCallback 1com.example.kpitrackerapp.ui.CompactReportAdapter  TaskDiffCallback 0com.example.kpitrackerapp.ui.EnhancedTaskAdapter  ExcelDiffCallback /com.example.kpitrackerapp.ui.ExcelReviewAdapter  DateAxisValueFormatter .com.example.kpitrackerapp.ui.KpiDetailActivity  KpiWithProgressDiffCallback +com.example.kpitrackerapp.ui.KpiListAdapter  ProgressEntryDiffCallback 4com.example.kpitrackerapp.ui.KpiProgressEntryAdapter  RecentUserDiffCallback /com.example.kpitrackerapp.ui.RecentUsersAdapter  ReportDiffCallback *com.example.kpitrackerapp.ui.ReportAdapter  ReportRowDiffCallback 1com.example.kpitrackerapp.ui.UnifiedReportAdapter  UserSummaryDiffCallback /com.example.kpitrackerapp.ui.UserSummaryAdapter  ChatViewModelFactory $com.example.kpitrackerapp.viewmodels  KpiViewModelFactory $com.example.kpitrackerapp.viewmodels  ProductViewModelFactory $com.example.kpitrackerapp.viewmodels  TaskViewModelFactory $com.example.kpitrackerapp.viewmodels  RecurringTaskWorker !com.example.kpitrackerapp.workers  	Companion 5com.example.kpitrackerapp.workers.RecurringTaskWorker  ValueFormatter &com.github.mikephil.charting.formatter  EnhancedIntentService com.google.firebase.messaging  FirebaseMessagingService com.google.firebase.messaging                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               