<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_expire_management" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_expire_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_expire_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="429" endOffset="53"/></Target><Target id="@+id/totalProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="27" startOffset="16" endLine="71" endOffset="67"/></Target><Target id="@+id/totalProductsCount" view="TextView"><Expressions/><location startLine="52" startOffset="24" endLine="59" endOffset="65"/></Target><Target id="@+id/expiredProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="74" startOffset="16" endLine="119" endOffset="67"/></Target><Target id="@+id/expiredProductsCount" view="TextView"><Expressions/><location startLine="100" startOffset="24" endLine="107" endOffset="64"/></Target><Target id="@+id/expiringProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="122" startOffset="16" endLine="166" endOffset="67"/></Target><Target id="@+id/expiringProductsCount" view="TextView"><Expressions/><location startLine="147" startOffset="24" endLine="154" endOffset="67"/></Target><Target id="@+id/foodCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="188" startOffset="16" endLine="230" endOffset="67"/></Target><Target id="@+id/foodCount" view="TextView"><Expressions/><location startLine="212" startOffset="24" endLine="219" endOffset="62"/></Target><Target id="@+id/medicineCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="233" startOffset="16" endLine="275" endOffset="67"/></Target><Target id="@+id/medicineCount" view="TextView"><Expressions/><location startLine="257" startOffset="24" endLine="264" endOffset="62"/></Target><Target id="@+id/babyCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="278" startOffset="16" endLine="320" endOffset="67"/></Target><Target id="@+id/babyCount" view="TextView"><Expressions/><location startLine="302" startOffset="24" endLine="309" endOffset="62"/></Target><Target id="@+id/cosmeticsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="323" startOffset="16" endLine="365" endOffset="67"/></Target><Target id="@+id/cosmeticsCount" view="TextView"><Expressions/><location startLine="347" startOffset="24" endLine="354" endOffset="62"/></Target><Target id="@+id/supplementsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="368" startOffset="16" endLine="410" endOffset="67"/></Target><Target id="@+id/supplementsCount" view="TextView"><Expressions/><location startLine="392" startOffset="24" endLine="399" endOffset="62"/></Target><Target id="@+id/addProductFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="419" startOffset="4" endLine="427" endOffset="51"/></Target></Targets></Layout>