<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_expiry_tracking" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_expiry_tracking.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_expiry_tracking_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="254" endOffset="53"/></Target><Target id="@+id/currentDateText" view="TextView"><Expressions/><location startLine="53" startOffset="20" endLine="61" endOffset="50"/></Target><Target id="@+id/expiredCountText" view="TextView"><Expressions/><location startLine="99" startOffset="24" endLine="107" endOffset="54"/></Target><Target id="@+id/expiringSoonCountText" view="TextView"><Expressions/><location startLine="138" startOffset="24" endLine="146" endOffset="54"/></Target><Target id="@+id/expiryActionCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="155" startOffset="12" endLine="212" endOffset="63"/></Target><Target id="@+id/actionMessageText" view="TextView"><Expressions/><location startLine="193" startOffset="24" endLine="200" endOffset="60"/></Target><Target id="@+id/viewExpiredProductsButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="221" startOffset="16" endLine="232" endOffset="45"/></Target><Target id="@+id/viewExpiringSoonButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="235" startOffset="16" endLine="246" endOffset="69"/></Target></Targets></Layout>