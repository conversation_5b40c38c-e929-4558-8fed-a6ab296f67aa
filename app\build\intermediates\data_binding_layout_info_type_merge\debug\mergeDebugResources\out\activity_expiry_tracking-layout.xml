<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_expiry_tracking" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_expiry_tracking.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_expiry_tracking_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="265" endOffset="53"/></Target><Target id="@+id/datePickerCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="31" startOffset="12" endLine="76" endOffset="63"/></Target><Target id="@+id/currentDateText" view="TextView"><Expressions/><location startLine="57" startOffset="20" endLine="65" endOffset="50"/></Target><Target id="@+id/expiredCountText" view="TextView"><Expressions/><location startLine="110" startOffset="24" endLine="118" endOffset="54"/></Target><Target id="@+id/expiringSoonCountText" view="TextView"><Expressions/><location startLine="149" startOffset="24" endLine="157" endOffset="54"/></Target><Target id="@+id/expiryActionCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="166" startOffset="12" endLine="223" endOffset="63"/></Target><Target id="@+id/actionMessageText" view="TextView"><Expressions/><location startLine="204" startOffset="24" endLine="211" endOffset="60"/></Target><Target id="@+id/viewExpiredProductsButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="232" startOffset="16" endLine="243" endOffset="45"/></Target><Target id="@+id/viewExpiringSoonButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="246" startOffset="16" endLine="257" endOffset="69"/></Target></Targets></Layout>