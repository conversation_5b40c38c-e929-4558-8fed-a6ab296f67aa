package com.example.kpitrackerapp.ui

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityExpiryTrackingBinding
import com.example.kpitrackerapp.persistence.AppDatabase
import com.example.kpitrackerapp.viewmodels.ProductViewModel
import com.example.kpitrackerapp.viewmodels.ProductViewModelFactory
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class ExpiryTrackingActivity : AppCompatActivity() {

    private lateinit var binding: ActivityExpiryTrackingBinding
    private val productViewModel: ProductViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        ProductViewModelFactory(
            application,
            database.productDao()
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityExpiryTrackingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupActionBar()
        setupCurrentDate()
        setupClickListeners()
        observeExpiredProducts()
    }

    private fun setupActionBar() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = "إدارة انتهاء الصلاحية"
    }

    private fun setupCurrentDate() {
        val dateFormat = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
        val currentDate = dateFormat.format(Date())
        binding.currentDateText.text = "التاريخ المحدد: $currentDate"
    }

    private fun setupClickListeners() {
        // Action card click - navigate to expired products
        binding.expiryActionCard.setOnClickListener {
            val intent = Intent(this, ProductListActivity::class.java).apply {
                putExtra(ProductListActivity.EXTRA_FILTER_TYPE, ProductListActivity.FILTER_EXPIRED)
            }
            startActivity(intent)
        }

        // View all expired products button
        binding.viewExpiredProductsButton.setOnClickListener {
            val intent = Intent(this, ProductListActivity::class.java).apply {
                putExtra(ProductListActivity.EXTRA_FILTER_TYPE, ProductListActivity.FILTER_EXPIRED)
            }
            startActivity(intent)
        }

        // View expiring soon products button
        binding.viewExpiringSoonButton.setOnClickListener {
            val intent = Intent(this, ProductListActivity::class.java).apply {
                putExtra(ProductListActivity.EXTRA_FILTER_TYPE, ProductListActivity.FILTER_EXPIRING_SOON)
            }
            startActivity(intent)
        }
    }

    private fun observeExpiredProducts() {
        lifecycleScope.launch {
            productViewModel.uiState.collect { state ->
                updateExpiryInfo(state)
            }
        }
    }

    private fun updateExpiryInfo(state: com.example.kpitrackerapp.viewmodels.ProductUiState) {
        // Update expired products count
        binding.expiredCountText.text = "${state.expiredProductsCount} منتج منتهي الصلاحية"
        
        // Update expiring soon count
        binding.expiringSoonCountText.text = "${state.expiringInWeekCount} منتج ينتهي خلال أسبوع"
        
        // Update action message based on current month
        val calendar = Calendar.getInstance()
        val monthName = when (calendar.get(Calendar.MONTH)) {
            Calendar.JANUARY -> "يناير"
            Calendar.FEBRUARY -> "فبراير"
            Calendar.MARCH -> "مارس"
            Calendar.APRIL -> "أبريل"
            Calendar.MAY -> "مايو"
            Calendar.JUNE -> "يونيو"
            Calendar.JULY -> "يوليو"
            Calendar.AUGUST -> "أغسطس"
            Calendar.SEPTEMBER -> "سبتمبر"
            Calendar.OCTOBER -> "أكتوبر"
            Calendar.NOVEMBER -> "نوفمبر"
            Calendar.DECEMBER -> "ديسمبر"
            else -> "الشهر الحالي"
        }
        
        binding.actionMessageText.text = "المطلوب: إرجاع منتهيات شهر $monthName"
        
        // Show/hide action card based on expired products
        if (state.expiredProductsCount > 0) {
            binding.expiryActionCard.visibility = android.view.View.VISIBLE
        } else {
            binding.expiryActionCard.visibility = android.view.View.GONE
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
