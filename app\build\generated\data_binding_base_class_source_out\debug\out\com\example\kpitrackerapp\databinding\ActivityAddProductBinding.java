// Generated by view binder compiler. Do not edit!
package com.example.kpitrackerapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.kpitrackerapp.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAddProductBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final MaterialButton addProductButton;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final Spinner categorySpinner;

  @NonNull
  public final TextInputEditText expiryDateInput;

  @NonNull
  public final TextInputEditText locationInput;

  @NonNull
  public final TextInputEditText notesInput;

  @NonNull
  public final TextInputEditText productNameInput;

  @NonNull
  public final TextInputEditText quantityInput;

  private ActivityAddProductBinding(@NonNull NestedScrollView rootView,
      @NonNull MaterialButton addProductButton, @NonNull MaterialButton cancelButton,
      @NonNull Spinner categorySpinner, @NonNull TextInputEditText expiryDateInput,
      @NonNull TextInputEditText locationInput, @NonNull TextInputEditText notesInput,
      @NonNull TextInputEditText productNameInput, @NonNull TextInputEditText quantityInput) {
    this.rootView = rootView;
    this.addProductButton = addProductButton;
    this.cancelButton = cancelButton;
    this.categorySpinner = categorySpinner;
    this.expiryDateInput = expiryDateInput;
    this.locationInput = locationInput;
    this.notesInput = notesInput;
    this.productNameInput = productNameInput;
    this.quantityInput = quantityInput;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAddProductBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAddProductBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_add_product, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAddProductBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addProductButton;
      MaterialButton addProductButton = ViewBindings.findChildViewById(rootView, id);
      if (addProductButton == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.categorySpinner;
      Spinner categorySpinner = ViewBindings.findChildViewById(rootView, id);
      if (categorySpinner == null) {
        break missingId;
      }

      id = R.id.expiryDateInput;
      TextInputEditText expiryDateInput = ViewBindings.findChildViewById(rootView, id);
      if (expiryDateInput == null) {
        break missingId;
      }

      id = R.id.locationInput;
      TextInputEditText locationInput = ViewBindings.findChildViewById(rootView, id);
      if (locationInput == null) {
        break missingId;
      }

      id = R.id.notesInput;
      TextInputEditText notesInput = ViewBindings.findChildViewById(rootView, id);
      if (notesInput == null) {
        break missingId;
      }

      id = R.id.productNameInput;
      TextInputEditText productNameInput = ViewBindings.findChildViewById(rootView, id);
      if (productNameInput == null) {
        break missingId;
      }

      id = R.id.quantityInput;
      TextInputEditText quantityInput = ViewBindings.findChildViewById(rootView, id);
      if (quantityInput == null) {
        break missingId;
      }

      return new ActivityAddProductBinding((NestedScrollView) rootView, addProductButton,
          cancelButton, categorySpinner, expiryDateInput, locationInput, notesInput,
          productNameInput, quantityInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
