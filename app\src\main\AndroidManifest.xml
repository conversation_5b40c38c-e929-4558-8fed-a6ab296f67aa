<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Required for notifications on Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <!-- Required for running foreground services (used by WorkManager when setForeground is called) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <!-- Required for Camera -->
    <uses-permission android:name="android.permission.CAMERA"/>
    <!-- Required for Calendar Integration -->
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <!-- Required for Speech Recognition -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Required for Location Services -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- Required for Setting Alarms -->
    <uses-permission android:name="android.permission.SET_ALARM" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />

    <!-- Declare camera feature, but don't require it if app can function without -->
    <uses-feature android:name="android.hardware.camera" android:required="false"/>

    <!-- Queries for specific apps we want to detect (Android 11+ requirement) -->
    <queries>
        <!-- WhatsApp Regular -->
        <package android:name="com.whatsapp" />
        <!-- WhatsApp Business -->
        <package android:name="com.whatsapp.w4b" />
        <!-- Gmail -->
        <package android:name="com.google.android.gm" />
        <!-- Email intent -->
        <intent>
            <action android:name="android.intent.action.SENDTO" />
            <data android:scheme="mailto" />
        </intent>
    </queries>

    <application
        android:name=".KpiTrackerApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.KPITrackerApp"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">

        <!-- Optional: Request OCR module download on install/update -->
        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="ocr" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"> <!-- Apply NoActionBar theme here -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.AddEditKpiActivity"
            android:label="@string/add_kpi_title"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- This is now for Tasks -->
        <activity
            android:name=".ui.AddEditKpiOriginalActivity"
            android:label="@string/add_kpi_title"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name=".ui.KpiDetailActivity"
            android:label="@string/kpi_detail_title"
            android:parentActivityName=".MainActivity" />
        <activity
            android:name=".ui.ModernReportActivity"
            android:label="Interactive Performance Report"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name=".ui.ExpireManagementActivity"
            android:label="@string/action_expiry_management"
            android:parentActivityName=".MainActivity" />
        <activity
            android:name=".ui.SearchEditProgressActivity"
            android:label="@string/search_edit_progress_title"
            android:parentActivityName=".ui.KpiDetailActivity" />
        <activity
            android:name=".ui.OcrActivity"
            android:label="@string/ocr_activity_title"
            android:parentActivityName=".MainActivity" />
        <activity
            android:name=".ui.OcrReviewActivity"
            android:label="@string/review_ocr_results_title"
            android:parentActivityName=".ui.OcrActivity" /> <!-- Parent is OcrActivity -->
        <!-- Removed SmartListAnalysisActivity declaration -->
        <activity
            android:name=".ui.ExcelImportActivity"
            android:label="Import from Excel"
            android:parentActivityName=".ui.KpiDetailActivity" />
        <activity
            android:name=".ui.ExcelReviewActivity"
            android:label="Review Excel Import"
            android:parentActivityName=".ui.ExcelImportActivity" />

        <activity
            android:name=".ui.UserKpiListActivity"
            android:label="@string/user_kpi_list_title"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" /> <!-- Apply NoActionBar theme -->
        <activity
            android:name=".ui.TaskManagementActivity"
            android:label="Task Management"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />
        <activity
            android:name=".ui.TaskReportActivity"
            android:label="Task Reports"
            android:parentActivityName=".ui.TaskManagementActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar" />

        <!-- Login and User Management Activities -->
        <activity
            android:name=".ui.LoginActivity"
            android:label="Login"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />
        <activity
            android:name=".ui.CreateUserActivity"
            android:label="Create User"
            android:parentActivityName=".ui.LoginActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Admin Dashboard Activity -->
        <activity
            android:name=".AdminDashboardActivity"
            android:label="Admin Dashboard"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Product Management Activities -->
        <activity
            android:name=".ui.AddProductActivity"
            android:label="Add Product"
            android:parentActivityName=".ui.ExpireManagementActivity"
            android:theme="@style/Theme.KPITrackerApp"
            android:exported="false" />
        <activity
            android:name=".ui.ProductListActivity"
            android:label="Product List"
            android:parentActivityName=".ui.ExpireManagementActivity"
            android:theme="@style/Theme.KPITrackerApp"
            android:exported="false" />
        <activity
            android:name=".ui.ExpiryTrackingActivity"
            android:label="Expiry Tracking"
            android:parentActivityName=".ui.ExpireManagementActivity"
            android:theme="@style/Theme.KPITrackerApp"
            android:exported="false" />

        <!-- Chat Activities -->
        <activity
            android:name=".ui.ChatListActivity"
            android:label="Messages"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <activity
            android:name=".ui.ChatActivity"
            android:label="Chat"
            android:parentActivityName=".ui.ChatListActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Date Converter Activity -->
        <activity
            android:name=".ui.DateConverterActivity"
            android:label="محول التاريخ"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Task Reminder Settings Activity -->
        <activity
            android:name=".ui.TaskReminderSettingsActivity"
            android:label="Task Reminder Settings"
            android:parentActivityName=".ui.TaskManagementActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Auto Send Settings Activity -->
        <activity
            android:name=".ui.AutoSendSettingsActivity"
            android:label="Auto Send Settings"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Advanced Task Activity -->
        <activity
            android:name=".ui.AddEditAdvancedTaskActivity"
            android:label="إضافة مهمة متقدمة"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Modern Add Task Activity -->
        <activity
            android:name=".ui.ModernAddTaskActivity"
            android:label="Add New Task"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false" />

        <!-- Pomodoro Timer Activity -->
        <activity
            android:name=".ui.PomodoroTimerActivity"
            android:label="Pomodoro Timer"
            android:parentActivityName=".ui.TaskManagementActivity"
            android:theme="@style/Theme.KPITrackerApp.NoActionBar"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- Add other activities, services, etc. here -->
        <activity
            android:name=".ui.NotificationsActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity" />

        <!-- Firebase Messaging Service -->
        <service
            android:name=".services.KPIFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Firebase Messaging default notification icon -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <!-- Firebase Messaging default notification color -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/purple_500" />

        <!-- FileProvider for sharing camera image URI -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Disable WorkManager automatic initialization since we use Configuration.Provider -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>
    </application>

</manifest>
