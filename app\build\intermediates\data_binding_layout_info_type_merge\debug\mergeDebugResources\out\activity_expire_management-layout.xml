<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_expire_management" modulePackage="com.example.kpitrackerapp" filePath="app\src\main\res\layout\activity_expire_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_expire_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="540" endOffset="53"/></Target><Target id="@+id/totalProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="27" startOffset="16" endLine="71" endOffset="67"/></Target><Target id="@+id/totalProductsCount" view="TextView"><Expressions/><location startLine="52" startOffset="24" endLine="59" endOffset="65"/></Target><Target id="@+id/expiredProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="74" startOffset="16" endLine="119" endOffset="67"/></Target><Target id="@+id/expiredProductsCount" view="TextView"><Expressions/><location startLine="100" startOffset="24" endLine="107" endOffset="64"/></Target><Target id="@+id/expiringProductsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="122" startOffset="16" endLine="166" endOffset="67"/></Target><Target id="@+id/expiringProductsCount" view="TextView"><Expressions/><location startLine="147" startOffset="24" endLine="154" endOffset="67"/></Target><Target id="@+id/medicineCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="188" startOffset="16" endLine="230" endOffset="67"/></Target><Target id="@+id/medicineCount" view="TextView"><Expressions/><location startLine="212" startOffset="24" endLine="219" endOffset="62"/></Target><Target id="@+id/momBabyCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="233" startOffset="16" endLine="275" endOffset="67"/></Target><Target id="@+id/momBabyCount" view="TextView"><Expressions/><location startLine="257" startOffset="24" endLine="264" endOffset="62"/></Target><Target id="@+id/personalCareCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="278" startOffset="16" endLine="320" endOffset="67"/></Target><Target id="@+id/personalCareCount" view="TextView"><Expressions/><location startLine="302" startOffset="24" endLine="309" endOffset="62"/></Target><Target id="@+id/nutraceuticalCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="323" startOffset="16" endLine="365" endOffset="67"/></Target><Target id="@+id/nutraceuticalCount" view="TextView"><Expressions/><location startLine="347" startOffset="24" endLine="354" endOffset="62"/></Target><Target id="@+id/beautyCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="368" startOffset="16" endLine="410" endOffset="67"/></Target><Target id="@+id/beautyCount" view="TextView"><Expressions/><location startLine="392" startOffset="24" endLine="399" endOffset="62"/></Target><Target id="@+id/currentDateText" view="TextView"><Expressions/><location startLine="453" startOffset="24" endLine="460" endOffset="69"/></Target><Target id="@+id/expiryActionCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="467" startOffset="16" endLine="507" endOffset="67"/></Target><Target id="@+id/openExpiryTrackingButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="510" startOffset="16" endLine="521" endOffset="45"/></Target><Target id="@+id/addProductFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="530" startOffset="4" endLine="538" endOffset="51"/></Target></Targets></Layout>