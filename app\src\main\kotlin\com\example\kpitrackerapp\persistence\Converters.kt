package com.example.kpitrackerapp.persistence

import androidx.room.TypeConverter
import com.example.kpitrackerapp.models.KpiUnit
import com.example.kpitrackerapp.models.ProductCategory
import java.util.Date

class Converters {
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }

    @TypeConverter
    fun fromKpiUnitName(value: String?): KpiUnit? {
        return value?.let { KpiUnit.valueOf(it) }
    }

    @TypeConverter
    fun kpiUnitToName(unit: KpiUnit?): String? {
        return unit?.name
    }

    @TypeConverter
    fun fromProductCategoryName(value: String?): ProductCategory? {
        return value?.let { ProductCategory.valueOf(it) }
    }

    @TypeConverter
    fun productCategoryToName(category: ProductCategory?): String? {
        return category?.name
    }
}
