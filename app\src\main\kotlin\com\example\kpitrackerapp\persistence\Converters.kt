package com.example.kpitrackerapp.persistence

import androidx.room.TypeConverter
import com.example.kpitrackerapp.models.KpiUnit
import com.example.kpitrackerapp.models.ProductCategory
import java.util.Date

class Converters {
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }

    @TypeConverter
    fun fromKpiUnitName(value: String?): KpiUnit? {
        return value?.let { KpiUnit.valueOf(it) }
    }

    @TypeConverter
    fun kpiUnitToName(unit: KpiUnit?): String? {
        return unit?.name
    }

    @TypeConverter
    fun fromProductCategoryName(value: String?): ProductCategory? {
        return value?.let { categoryName ->
            try {
                ProductCategory.valueOf(categoryName)
            } catch (e: IllegalArgumentException) {
                // Handle old category names that no longer exist
                when (categoryName) {
                    "FOOD" -> ProductCategory.MEDICINE // Map old FOOD to MEDICINE
                    "COSMETICS" -> ProductCategory.BEAUTY // Map old COSMETICS to BEAUTY
                    "BABY" -> ProductCategory.MOM_BABY // Map old BABY to MOM_BABY
                    "HEALTH" -> ProductCategory.MEDICINE // Map old HEALTH to MEDICINE
                    "SUPPLEMENTS" -> ProductCategory.NUTRACEUTICAL // Map old SUPPLEMENTS to NUTRACEUTICAL
                    else -> ProductCategory.MEDICINE // Default fallback
                }
            }
        }
    }

    @TypeConverter
    fun productCategoryToName(category: ProductCategory?): String? {
        return category?.name
    }
}
