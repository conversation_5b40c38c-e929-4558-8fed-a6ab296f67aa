package com.example.kpitrackerapp.ui

import android.app.DatePickerDialog
import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.kpitrackerapp.R
import com.example.kpitrackerapp.databinding.ActivityAddProductBinding
import com.example.kpitrackerapp.models.Product
import com.example.kpitrackerapp.models.ProductCategory
import com.example.kpitrackerapp.viewmodels.ProductViewModel
import com.example.kpitrackerapp.viewmodels.ProductViewModelFactory
import com.example.kpitrackerapp.persistence.AppDatabase
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class AddProductActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAddProductBinding
    private val productViewModel: ProductViewModel by viewModels {
        val database = AppDatabase.getDatabase(this)
        ProductViewModelFactory(
            application,
            database.productDao()
        )
    }
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private var selectedExpiryDate: Date? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddProductBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupActionBar()
        setupCategorySpinner()
        setupClickListeners()
        observeViewModel()
    }

    private fun setupActionBar() {
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = "إضافة منتج جديد"
    }

    private fun setupCategorySpinner() {
        val categories = ProductCategory.values().map { "${it.emoji} ${it.displayName}" }
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categories)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.categorySpinner.adapter = adapter
    }

    private fun setupClickListeners() {
        binding.expiryDateInput.setOnClickListener {
            showDatePickerDialog()
        }

        binding.addProductButton.setOnClickListener {
            addProduct()
        }

        binding.cancelButton.setOnClickListener {
            finish()
        }
    }

    private fun showDatePickerDialog() {
        val calendar = Calendar.getInstance()

        val dateSetListener = DatePickerDialog.OnDateSetListener { _, year, month, dayOfMonth ->
            calendar.set(Calendar.YEAR, year)
            calendar.set(Calendar.MONTH, month)
            calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

            selectedExpiryDate = calendar.time
            binding.expiryDateInput.setText(dateFormat.format(calendar.time))
        }

        DatePickerDialog(
            this,
            dateSetListener,
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun addProduct() {
        val name = binding.productNameInput.text.toString().trim()
        val quantityText = binding.quantityInput.text.toString().trim()
        val location = binding.locationInput.text.toString().trim()
        val notes = binding.notesInput.text.toString().trim()

        // Validation
        if (name.isEmpty()) {
            binding.productNameInput.error = "اسم المنتج مطلوب"
            return
        }

        if (quantityText.isEmpty()) {
            binding.quantityInput.error = "الكمية مطلوبة"
            return
        }

        val quantity = quantityText.toIntOrNull()
        if (quantity == null || quantity <= 0) {
            binding.quantityInput.error = "الكمية يجب أن تكون رقم صحيح أكبر من صفر"
            return
        }

        if (selectedExpiryDate == null) {
            Toast.makeText(this, "تاريخ انتهاء الصلاحية مطلوب", Toast.LENGTH_SHORT).show()
            return
        }

        val selectedCategory = ProductCategory.values()[binding.categorySpinner.selectedItemPosition]

        val product = Product(
            name = name,
            category = selectedCategory,
            expiryDate = selectedExpiryDate!!,
            quantity = quantity,
            location = location.ifEmpty { null },
            notes = notes.ifEmpty { null }
        )

        productViewModel.insertProduct(product)
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            productViewModel.uiState.collect { state ->
                if (state.isLoading) {
                    binding.addProductButton.isEnabled = false
                    binding.addProductButton.text = "جاري الإضافة..."
                } else {
                    binding.addProductButton.isEnabled = true
                    binding.addProductButton.text = "إضافة المنتج"
                }

                state.message?.let { message ->
                    Toast.makeText(this@AddProductActivity, message, Toast.LENGTH_SHORT).show()
                    productViewModel.clearMessage()
                    finish() // Go back to dashboard
                }

                state.error?.let { error ->
                    Toast.makeText(this@AddProductActivity, error, Toast.LENGTH_LONG).show()
                    productViewModel.clearError()
                }
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
