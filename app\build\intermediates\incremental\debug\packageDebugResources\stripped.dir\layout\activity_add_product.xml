<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:padding="16dp"
    tools:context=".ui.AddProductActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Product Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="اسم المنتج *"
            app:startIconDrawable="@drawable/ic_dashboard_24"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/productNameInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1"
                tools:text="Test1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Category -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="الفئة *"
            app:startIconDrawable="@drawable/ic_baseline_category_24"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

            <Spinner
                android:id="@+id/categorySpinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:padding="12dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Expiry Date -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="تاريخ انتهاء الصلاحية *"
            app:startIconDrawable="@drawable/ic_time"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/expiryDateInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:clickable="true"
                android:inputType="none"
                android:hint="YYYY-MM-DD"
                tools:text="2025-12-31" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Quantity and Location Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Quantity -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="الكمية"
                app:startIconDrawable="@drawable/ic_dashboard_24"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/quantityInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:maxLines="1"
                    tools:text="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Location -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="الموقع"
                app:startIconDrawable="@drawable/ic_search"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/locationInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1"
                    tools:text="الرف 1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Notes -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:hint="ملاحظات"
            app:startIconDrawable="@drawable/ic_messages_24"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/notesInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:maxLines="3"
                android:hint="ملاحظات إضافية حول هذا المنتج"
                tools:text="ملاحظات إضافية حول هذا المنتج" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- Add Product Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/addProductButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="إضافة المنتج"
                android:textSize="16sp"
                android:textStyle="bold"
                app:icon="@drawable/ic_check"
                app:iconGravity="textStart"
                style="@style/Widget.MaterialComponents.Button" />

            <!-- Cancel Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/cancelButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="إلغاء"
                android:textSize="16sp"
                app:icon="@drawable/ic_delete"
                app:iconGravity="textStart"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
